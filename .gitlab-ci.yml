image: registry.gitlab.benzinga.io/benzinga/docker/nikolaik/python-nodejs:python3.11-nodejs18

workflow:
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != null
      when: always
    - if: $CI_COMMIT_TAG != null
      when: always
    - if: $CLEAN_DOCKER == "true"
      when: always
    - when: never

stages:
  - clean
  - check
  # - setup
  - build
  - image
  - deploy
  - cache
  - test

.cache: &global_cache
  untracked: true
  key: one-key-to-rule-them-all-nx-18-1-test-3
  paths:
    - node_modules/
  policy: pull-push

# Variables

variables: &global_variables
  DOCKER_DRIVER: overlay2
  DOCKER_TLS_CERTDIR: ""
  FF_USE_FASTZIP: "true" # https://gitlab.com/gitlab-org/gitlab-runner/-/issues/3354
  GIT_DEPTH: 30 # https://docs.gitlab.com/ee/ci/large_repositories/#shallow-cloning
  KUBERNETES_CPU_REQUEST: 1
  KUBERNETES_MEMORY_LIMIT: 6Gi
  KUBERNETES_MEMORY_REQUEST: 6Gi
  YARN_CACHE_FOLDER: "$CI_PROJECT_DIR/.yarn-cache"
  # NEXT_CONTAINER_REPO: "$ECR_REGISTRY/benzinga/benzinga-next"
  NEXT_CONTAINER_REPO: "$CI_REGISTRY_IMAGE/benzinga-next"
  INDIA_CONTAINER_REPO: "$CI_REGISTRY_IMAGE/benzinga-india"
  MONEY_CONTAINER_REPO: "$CI_REGISTRY_IMAGE/benzinga-money"
  PROTO_CONTAINER_REPO: "$CI_REGISTRY_IMAGE/benzinga-proto"
  # https://docs.gitlab.com/ee/ci/runners/configure_runners.html#job-stages-attempts
  ARTIFACT_DOWNLOAD_ATTEMPTS: 2
  EXECUTOR_JOB_SECTION_ATTEMPTS: 2
  GET_SOURCES_ATTEMPTS: 2
  RESTORE_CACHE_ATTEMPTS: 2
  TRANSFER_METER_FREQUENCY: "30s"
  KUBE_CONTEXT: fusion # The name to use for the new context
  AGENT_ID: 12 # replace with your agent's numeric ID
  K8S_PROXY_URL: https://gitlab.benzinga.io/-/kubernetes-agent/k8s-proxy/
  KUBE_NAMESPACE: fusion-195-staging
  NX_DAEMON: "false"
  AWS_ROLE_ARN: $ROLE_ARN

#
# Templates
#

.aws_cli: &aws_cli
  image: registry.gitlab.benzinga.io/benzinga/docker/python:3.9-slim-bullseye
  id_tokens:
    GITLAB_OIDC_TOKEN:
      aud: https://gitlab.benzinga.io
  variables:
    AWS_ROLE_ARN: $ROLE_ARN
  before_script:
    - pip install awscli httpie
    - >
      export $(printf "AWS_ACCESS_KEY_ID=%s AWS_SECRET_ACCESS_KEY=%s AWS_SESSION_TOKEN=%s"
      $(aws sts assume-role-with-web-identity
      --role-arn ${AWS_ROLE_ARN}
      --role-session-name "GitLabRunner-${CI_PROJECT_ID}-${CI_PIPELINE_ID}"
      --web-identity-token ${GITLAB_OIDC_TOKEN}
      --duration-seconds 900
      --query 'Credentials.[AccessKeyId,SecretAccessKey,SessionToken]'
      --output text))
    - aws sts get-caller-identity

.bootstrap: &bootstrap
  - export NX_DAEMON=false
  - yarn config set registry "$NPM_REGISTRY_URL_V1"
  - yarn config set //$NPM_REGISTRY_URL_V1:_authToken $NPM_AUTH_TOKEN
  - yarn config set always-auth true
  - yarn install --frozen-lockfile --network-timeout 1000000000

.config_npm: &config_npm |
  npm config set @benzinga:registry https://gitlab.benzinga.io/api/v4/projects/${CI_PROJECT_ID}/packages/npm/
  npm config set '//gitlab.benzinga.io/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken' "${CI_JOB_TOKEN}"
  echo "//gitlab.benzinga.io/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}">.npmrc

.retry: &retry
  max: 2
  when:
    - runner_system_failure
    - stuck_or_timeout_failure

# clean

clean-docker:
  image: docker:20.10.11
  stage: clean
  tags:
    - project-mihail
  rules:
    - if: $CLEAN_DOCKER == "true"
  services:
    - docker:dind
  cache: *global_cache
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - rm -rf ./.nx
    - rm -rf ./node_modules
    - docker system prune -f -a --volumes;

# lint

lint_packages:
  tags:
    - project-mihail
  stage: build
  cache: *global_cache
  script:
    - *bootstrap
    - $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH || [ ! -z "$CI_COMMIT_TAG" ] || yarn changelog:check-changelog-exists
    - yarn nx run-many --target=lint --parallel --all --exclude=bz-mobile,bz-mobile-e2e,pro-e2e,widgets-pro-insiders,tsmd,third-party-chartiq,third-party-charting-library --quiet
  rules:
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != null
      when: always
  retry: *retry

# build
#build:cross-env:
#  cache: *global_cache
#  tags:
#    - project-mihail
#  script:
#    - yarn cross-env NX_DAEMON=true NODE_OPTIONS=--max_old_space_size=65536 yarn nx run-many --target=build --projects=bz,bz-changelog,bz-e2e,data-managers-examples,decode-quote-messages-indexes,india,money,newsdesk-tools,pro,pro-e2e,proto,tsmd,widgets-pro-calendar,widgets-pro-insiders --prod
#  stage: build

build:changed-apps-projects:
  artifacts:
    name: "fusion-$CI_COMMIT_REF_SLUG"
    untracked: false
    expire_in: 3 hours
    paths:
      - dist
    reports:
      dotenv: built.env
  cache: *global_cache
  tags:
    - project-mihail
  script:
    - *bootstrap
    - $CI_COMMIT_REF_NAME == $CI_DEFAULT_BRANCH || [ ! -z "$CI_COMMIT_TAG" ] || yarn changelog:check-changelog-exists
    - git fetch origin master:master
    - yarn changelog:check-missing-changelog && yarn changelog:build;
    - printenv | grep BUILT > built.env || echo ''
  stage: build
  retry: *retry
  rules:
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME != null
      when: always
    - if: $CI_COMMIT_TAG != null
      when: always
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - when: manual

# Image

image:bz:
  image: docker:20.10.11
  stage: image
  tags:
    - project-mihail
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  services:
    - docker:dind
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  artifacts:
    name: "image-money-$CI_COMMIT_REF_SLUG"
    untracked: false
    expire_in: 3 hours
    paths:
      - dist/apps/bz/package.json
  script:
    - printenv | grep BUILT || echo ''
    - if [ $BUILT_bz != "true" ]; then echo "project bz does not exists."; exit 1; fi;
    - if [ -n "$CI_COMMIT_TAG" ] && echo "$CI_COMMIT_TAG" | grep -qv "benzinga-next";
      then
      echo "Has a tag, and does not match benzinga-next, exiting...";
      exit 0;
      fi
    - rm .dockerignore
    # - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker build
      -f ./apps/bz/Dockerfile
      -t "$NEXT_CONTAINER_REPO:latest"
      --cache-from "$NEXT_CONTAINER_REPO:latest"
      --build-arg NPM_REGISTRY_URL="${NPM_REGISTRY_URL_V1}"
      --build-arg NPM_AUTH_TOKEN="${NPM_AUTH_TOKEN}" .
    - for TAG in r${CI_COMMIT_SHA:0:8} ${CI_COMMIT_TAG};
      do
      TMP_TAG=${TAG##@benzinga/benzinga-next};
      IMAGE_TAG=${TMP_TAG//@/v}-${CI_PIPELINE_ID};
      docker tag "$NEXT_CONTAINER_REPO:latest" "$NEXT_CONTAINER_REPO:$IMAGE_TAG" &&
      docker push "$NEXT_CONTAINER_REPO:$IMAGE_TAG" &&
      echo "Pushed ${TAG} (as ${IMAGE_TAG}) successfully.";
      done;
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/(bzfe|bz)-.*/i
      when: always
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/bz\x40.*$/
      when: always
    - when: manual
      allow_failure: true

image:money:
  image: docker:20.10.11
  stage: image
  tags:
    - project-mihail
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  artifacts:
    name: "image-money-$CI_COMMIT_REF_SLUG"
    untracked: false
    expire_in: 3 hours
    paths:
      - dist/apps/money/package.json
  services:
    - docker:dind
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - if [ $BUILT_money != "true" ]; then echo "project money does not exists."; exit 1; fi;
    - if [ -n "$CI_COMMIT_TAG" ] && echo "$CI_COMMIT_TAG" | grep -qv "money-next";
      then
      echo "Has a tag, and does not match money-next, exiting...";
      exit 0;
      fi
    - rm .dockerignore
    # - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker build
      -f ./apps/money/Dockerfile
      -t "$MONEY_CONTAINER_REPO:latest"
      --cache-from "$MONEY_CONTAINER_REPO:latest"
      --build-arg NPM_REGISTRY_URL="${NPM_REGISTRY_URL_V1}"
      --build-arg NPM_AUTH_TOKEN="${NPM_AUTH_TOKEN}" .
    - for TAG in r${CI_COMMIT_SHA:0:8} ${CI_COMMIT_TAG};
      do
      TMP_TAG=${TAG##@benzinga/money-next};
      IMAGE_TAG=${TMP_TAG//@/v}-${CI_PIPELINE_ID};
      docker tag "$MONEY_CONTAINER_REPO:latest" "$MONEY_CONTAINER_REPO:$IMAGE_TAG" &&
      docker push "$MONEY_CONTAINER_REPO:$IMAGE_TAG" &&
      echo "Pushed ${TAG} (as ${IMAGE_TAG}) successfully.";
      done;
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/(money|MNY)-.*/
      when: always
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/money\x40.*$/
    - when: manual
      allow_failure: true

image:india:
  image: docker:20.10.11
  stage: image
  tags:
    - project-mihail
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  services:
    - docker:dind
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  artifacts:
    name: "image-money-$CI_COMMIT_REF_SLUG"
    untracked: false
    expire_in: 3 hours
    paths:
      - dist/apps/india/package.json
  script:
    - if [ $BUILT_india != "true" ]; then echo "project india does not exists."; exit 1; fi;
    - if [ -n "$CI_COMMIT_TAG" ] && echo "$CI_COMMIT_TAG" | grep -qv "benzinga-next";
      then
      echo "Has a tag, and does not match benzinga-next, exiting...";
      exit 0;
      fi
    - rm .dockerignore
    # - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker build
      -f ./apps/india/Dockerfile
      -t "$INDIA_CONTAINER_REPO:latest"
      --cache-from "$INDIA_CONTAINER_REPO:latest"
      --build-arg NPM_REGISTRY_URL="${NPM_REGISTRY_URL_V1}"
      --build-arg NPM_AUTH_TOKEN="${NPM_AUTH_TOKEN}" .
    - for TAG in r${CI_COMMIT_SHA:0:8} ${CI_COMMIT_TAG};
      do
      TMP_TAG=${TAG##@benzinga/benzinga-next};
      IMAGE_TAG=${TMP_TAG//@/v}-${CI_PIPELINE_ID};
      docker tag "$INDIA_CONTAINER_REPO:latest" "$INDIA_CONTAINER_REPO:$IMAGE_TAG" &&
      docker push "$INDIA_CONTAINER_REPO:$IMAGE_TAG" &&
      echo "Pushed ${TAG} (as ${IMAGE_TAG}) successfully.";
      done;
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/india-.*/
      when: always
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/india\x40.*$/
    - when: manual
      allow_failure: true

image:proto:
  image: docker:20.10.11
  stage: image
  tags:
    - project-mihail
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  services:
    - docker:dind
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  artifacts:
    name: "image-money-$CI_COMMIT_REF_SLUG"
    untracked: false
    expire_in: 3 hours
    paths:
      - dist/apps/proto/package.json
  script:
    - if [ $BUILT_proto != "true" ]; then echo "project proto does not exists."; exit 1; fi;
    - if [ -n "$CI_COMMIT_TAG" ] && echo "$CI_COMMIT_TAG" | grep -qv "benzinga-next";
      then
      echo "Has a tag, and does not match benzinga-next, exiting...";
      exit 0;
      fi
    - rm .dockerignore
    # - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker login -u gitlab-ci-token -p "$CI_JOB_TOKEN" "$CI_REGISTRY"
    - docker build
      -f ./apps/proto/Dockerfile
      -t "$PROTO_CONTAINER_REPO:latest"
      --cache-from "$PROTO_CONTAINER_REPO:latest"
      --build-arg NPM_REGISTRY_URL="${NPM_REGISTRY_URL_V1}"
      --build-arg NPM_AUTH_TOKEN="${NPM_AUTH_TOKEN}" .
    - for TAG in r${CI_COMMIT_SHA:0:8} ${CI_COMMIT_TAG};
      do
      TMP_TAG=${TAG##@benzinga/benzinga-next};
      IMAGE_TAG=${TMP_TAG//@/v}-${CI_PIPELINE_ID};
      docker tag "$PROTO_CONTAINER_REPO:latest" "$PROTO_CONTAINER_REPO:$IMAGE_TAG" &&
      docker push "$PROTO_CONTAINER_REPO:$IMAGE_TAG" &&
      echo "Pushed ${TAG} (as ${IMAGE_TAG}) successfully.";
      done;
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: always
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/proto-.*/
      when: always
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/proto\x40.*$/
    - when: manual
      allow_failure: true

# Deploy

deploy:pro-sandbox:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: sandbox/pro/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.sbx.benzinga.com
    on_stop: undeploy:pro-sandbox
    auto_stop_in: 90 days
  dependencies:
    - build:changed-apps-projects
  needs: ["build:changed-apps-projects"]
  script:
    - if [ $BUILT_pro != "true" ]; then echo "project pro does not exists."; exit 1; fi;
    - echo "Deploying pro-frontend ${CI_COMMIT_REF_SLUG} ..."
    - aws s3 rm s3://bz-fusion-sandbox-deploys/${CI_COMMIT_REF_SLUG} --recursive || echo "Nothing to remove first." # remove all
    - aws s3 sync ./dist/apps/pro/ s3://bz-fusion-sandbox-deploys/${CI_COMMIT_REF_SLUG} --storage-class REDUCED_REDUNDANCY --metadata service=pro-frontend,environment=sandbox,surrogate-key=${CI_COMMIT_REF_SLUG},build=${CI_COMMIT_SHORT_SHA} || echo 0
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty=all --json --ignore-stdin POST "https://api.fastly.com/service/7KnfV8pc4tS5AVhvCC6OJ0/purge/${CI_COMMIT_REF_SLUG}" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" || echo 0
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --pretty=all --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Fusion/Pro-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}" || echo 0
    - DEPLOYED_pro_sandbox=true export DEPLOYED_pro_sandbox
  retry: *retry
  rules:
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/([Pp][Rr][Oo]|[Pp][Ff][Ee])-.*/
      when: on_success
    - when: manual
      allow_failure: true

undeploy:pro-sandbox:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  when: manual
  variables:
    <<: *global_variables
    GIT_STRATEGY: none # Dont pull in repo files
  environment:
    name: sandbox/pro/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.sbx.benzinga.com
    action: stop
  rules:
    - if: $DEPLOYED_pro_sandbox == "true"
  script:
    - echo "Deleting S3 deployment pro-frontend ${CI_COMMIT_REF_SLUG} ..."
    - aws s3 rm s3://bz-fusion-sandbox-deploys/${CI_COMMIT_REF_SLUG}/ --recursive
    # Purge Fastly Cache - key allows purge access only to single service
    - sleep 10
    - http --verify=no --pretty=all --json POST "https://api.fastly.com/service/7KnfV8pc4tS5AVhvCC6OJ0/purge/${CI_COMMIT_REF_SLUG}" 'Fastly-Key':"${FASTLY_SANDBOX_KEY}"

deploy:pro-staging:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: staging
    url: https://pro.zingbot.bz
  dependencies:
    - build:changed-apps-projects
  needs: ["build:changed-apps-projects"]
  script:
    - if [ $BUILT_pro != "true" ]; then echo "project pro does not exists."; exit 1; fi;
    # bz-fusion-pro-zingbot-bz.s3-website-us-east-1.amazonaws.com
    - aws s3 rm s3://bz-fusion-pro-zingbot-bz/ --recursive # remove all
    - aws s3 sync ./dist/apps/pro/ s3://bz-fusion-pro-zingbot-bz/ --exact-timestamps --metadata service=pro-frontend,environment=staging,surrogate-key=fusion-${CI_ENVIRONMENT_NAME},build=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    # Purge Fastly Cache - key allows purge access only to single service
    - sleep 10
    - http --verify=no --pretty=all --json --ignore-stdin POST "https://api.fastly.com/service/1nWmeiCqYsdv453jCgAs0O/purge/fusion-${CI_ENVIRONMENT_NAME}" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}"
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --pretty=all --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Fusion/Pro-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
    - DEPLOYED_pro_staging=true export DEPLOYED_pro_staging
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success

deploy:pro-beta:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: beta
    url: https://probeta.benzinga.com
  dependencies:
    - build:changed-apps-projects
  needs: ["build:changed-apps-projects"]
  script:
    - if [ $BUILT_pro != "true" ]; then echo "project pro does not exists."; exit 1; fi;
    # http://bz-fusion-probeta-benzinga-com.s3-website-us-east-1.amazonaws.com
    - aws s3 sync ./dist/apps/pro/ s3://bz-fusion-probeta-benzinga-com/ --exact-timestamps --metadata service=pro-frontend,environment=beta,surrogate-key=fusion-${CI_ENVIRONMENT_NAME},build=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - sleep 30
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty=all --json --ignore-stdin POST "https://api.fastly.com/service/72XVaC0bYNY6SItDeKEL42/purge/fusion-${CI_ENVIRONMENT_NAME}" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" 'Fastly-Soft-Purge':'1'
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --pretty=all --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Fusion/Pro-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to environment ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
    - DEPLOYED_pro_beta=true export DEPLOYED_pro_beta
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success

deploy:pro-production:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: production
    url: https://pro.benzinga.com
  dependencies:
    - build:changed-apps-projects
  needs: ["build:changed-apps-projects"]
  script:
    - if [ ! -d "dist/apps/pro" ]; then echo "project pro does not exists."; exit 1; fi;
    # http://bz-fusion-pro-benzinga-com.s3-website-us-east-1.amazonaws.com
    - aws s3 sync ./dist/apps/pro/ s3://bz-fusion-pro-benzinga-com/ --exact-timestamps --metadata service=pro-frontend,environment=production,surrogate-key=fusion-${CI_ENVIRONMENT_NAME},build=${CI_COMMIT_REF_SLUG}-${CI_COMMIT_SHORT_SHA}
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - sleep 30
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty=all --json --ignore-stdin POST "https://api.fastly.com/service/7lIhUuiw6tTEjNCZfJcivw/purge/fusion-${CI_ENVIRONMENT_NAME}" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" 'Fastly-Soft-Purge':'1'
    # Update current build API
    - echo 'Updating version check API.'
    - http --verify=no --pretty=all --json --ignore-stdin PUT 'https://api.fastly.com/service/1t3xcqiWZXWGNPOhK1xULw/dictionary/3YnRXoZTSAlFBmuL3qZtPX/item/latest_pfe_release_build' 'Fastly-Key':"${FASTLY_API_KEY}" 'Content-Type':'application/json; charset=utf-8' item_value=$(echo "${CI_COMMIT_REF_SLUG}" | sed 's/\(@benzinga\/pro-frontend@\)//g')
    - http --verify=no --pretty=all --json --ignore-stdin PUT 'https://api.fastly.com/service/1t3xcqiWZXWGNPOhK1xULw/dictionary/3YnRXoZTSAlFBmuL3qZtPX/item/latest_pfe_release_timestamp_iso8601' 'Fastly-Key':"${FASTLY_API_KEY}" 'Content-Type':'application/json; charset=utf-8' item_value=$(date -u +%FT%TZ)
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --pretty=all --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Fusion/Pro-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to environment ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
    - DEPLOYED_pro_production=true export DEPLOYED_pro_production
  retry: *retry
  rules:
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/pro\x40.*$/

deploy:bz-production:
  tags:
    - project-mihail
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  needs: ["image:bz"]
  dependencies: ["image:bz"]
  environment:
    name: production/bzx/${CI_COMMIT_REF_SLUG}
    url: https://quotes.benzinga.com
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - if [ ! -d "dist/apps/bz" ]; then echo "project bz does not exists."; exit 1; fi;
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
    - echo "Attempting to install $CI_REGISTRY_IMAGE/benzinga-next:r${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_URL}"
    - helm uninstall "${CI_COMMIT_REF_SLUG}" --namespace production || echo "No previous deployment for this branch."
    - helm install "${CI_COMMIT_REF_SLUG}" ./apps/bz/chart/benzinga-next -f ././apps/bz/chart/values/production.yaml --set "image.repository=${CI_REGISTRY_IMAGE}/benzinga-next" --set "image.tag=r${CI_COMMIT_SHA:0:8}-${CI_PIPELINE_ID}" --namespace production
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG}" --namespace production;
      echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - DEPLOYED_bz_production=true export DEPLOYED_bz_production
  retry: *retry
  rules:
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/bz\x40.*$/

deploy:bz-static-production:
  <<: *aws_cli
  stage: deploy
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  script:
    # Use cp here to reset the object timestamp and expiration.  Lifecycle policy is 120days (currently).
    - if [ $BUILT_bz != "true" ]; then echo "project bz does not exists."; exit 1; fi;
    - echo "Syncing Next static assets to s3"
    - aws s3 cp ./dist/apps/bz/.next/static/ s3://bz-fusion-next-dotcom/_next/static/ --recursive --storage-class REDUCED_REDUNDANCY --metadata service=benzinga
    - DEPLOYED_bz_static_production=true export DEPLOYED_bz_static_production
  variables:
    <<: *global_variables
  tags:
    - project-mihail
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^release\/bz-.*/
      when: on_success
    - when: manual
      allow_failure: true

deploy:bz-sandbox:
  allow_failure: true
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.bz.benzinga.dev
    HOSTNAME_1: ${CI_COMMIT_REF_SLUG}.bzsbx.zingbot.bz
    HOSTNAME_2: ${CI_COMMIT_REF_SLUG}.bzsbx.benzinga.com
  tags:
    - eks
  environment:
    name: sandbox/bzx/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.bzsbx.benzinga.com
    auto_stop_in: 1 week
    on_stop: undeploy:bz-sandbox
  before_script:
    - echo 'ignoring before_script'
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  retry: *retry
  script:
    - if [ $BUILT_bz != "true" ]; then echo "project bz does not exists."; exit 1; fi;
    - echo "Attempting to install $NEXT_CONTAINER_REPO:r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID} to $CI_ENVIRONMENT_URL"
    - chmod 777 ./scripts/slugify.sh
    - export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
    - export CI_COMMIT_REF_SLUG_SLUGIFY=b-$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/bz/chart/benzinga-next -f ./apps/bz/chart/values/dev-sandbox.yaml
      --set "image.repository=${NEXT_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set "ingress.hosts[2]=${HOSTNAME_2}"
      --set "environment=sandbox"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_bz_sandbox=true export DEPLOYED_bz_sandbox
  needs: ["image:bz"]
  dependencies: ["image:bz"]
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/(BZFE|bzfe|bz)-.*/
      when: on_success
    - when: manual
      allow_failure: true
undeploy:bz-sandbox:
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  tags:
    - project-mihail
  when: manual
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.nxt.benzinga.dev
    GIT_STRATEGY: none
  environment:
    name: sandbox/bzx/${CI_COMMIT_REF_SLUG}
    url: https://${HOSTNAME}
    action: stop
  rules:
    - if: $DEPLOYED_bz_sandbox == "true"
  before_script:
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  script:
    - |
      export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
      export CI_COMMIT_REF_SLUG_SLUGIFY=$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
      echo "Deleting helm deployment ${CI_COMMIT_REF_SLUG_SLUGIFY} ..."
      helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    # - kubectl delete namespace $KUBE_NAMESPACE || "Nothing to delete."

deploy:bz-beta:
  allow_failure: true
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  needs: ["image:bz"]
  dependencies: ["image:bz"]
  variables:
    <<: *global_variables
    HOSTNAME: wwwbeta.benzinga.com
    HOSTNAME_1: beta.benzinga.com
  tags:
    - project-mihail
  environment:
    name: beta/bzx/${CI_COMMIT_REF_SLUG}
    url: https://wwwbeta.benzinga.com
  before_script:
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  retry: *retry
  script:
    - if [ $BUILD_bz != "true" ]; then echo "project bz does not exists."; exit 1; fi;
    - echo "Attempting to install $NEXT_CONTAINER_REPO:r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID} to $CI_ENVIRONMENT_URL"
    - export CI_COMMIT_REF_SLUG_SLUGIFY="bzbeta-master"
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/bz/chart/benzinga-next -f ././apps/bz/chart/values/dev-sandbox.yaml
      --set "image.repository=${NEXT_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_bz_beta=true export DEPLOYED_bz_beta
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success

deploy:india-beta:
  tags:
    - project-mihail
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  needs: ["image:india"]
  dependencies: ["image:india"]
  environment:
    name: production/india/${CI_COMMIT_REF_SLUG}
    url: https://inbeta.benzinga.com
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
    HOSTNAME: inbeta.benzinga.com
  script:
    - if [ $BUILT_india != "true" ]; then echo "project india does not exists."; exit 1; fi;
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
    - echo "Attempting to install $INDIA_CONTAINER_REPO:r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID} to $CI_ENVIRONMENT_URL"
    - export CI_COMMIT_REF_SLUG_SLUGIFY="inbeta-master"
    - echo helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/india/chart/india-next -f ././apps/india/chart/values/dev-sandbox.yaml
      --set "image.repository=${INDIA_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/india/chart/india-next -f ././apps/india/chart/values/dev-sandbox.yaml
      --set "image.repository=${INDIA_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_india_beta=true export DEPLOYED_india_beta
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success

deploy:india-static-production:
  <<: *aws_cli
  stage: deploy
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  script:
    # Use cp here to reset the object timestamp and expiration.  Lifecycle policy is 120days (currently).
    - echo "Syncing Next static assets to s3"
    - aws s3 cp ./dist/apps/india/.next/static/ s3://bz-fusion-next-dotcom/_next/static/ --recursive --storage-class REDUCED_REDUNDANCY --metadata service=benzinga
    - DEPLOYED_india_static_production=true export DEPLOYED_india_static_production
  variables:
    <<: *global_variables
  tags:
    - project-mihail
  rules:
    - if: $BUILT_india == "true"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^release\/india-.*/
      when: on_success
    - when: manual
      allow_failure: true

deploy:india-sandbox:
  allow_failure: true
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.india.benzinga.dev
    HOSTNAME_1: ${CI_COMMIT_REF_SLUG}.indiasbx.zingbot.bz
    HOSTNAME_2: ${CI_COMMIT_REF_SLUG}.indiasbx.benzinga.com
  tags:
    - project-mihail
  environment:
    name: sandbox/india/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.indiasbx.benzinga.com
    auto_stop_in: 1 week
    on_stop: undeploy:india-sandbox
  needs: ["image:india"]
  dependencies: ["image:india"]
  before_script:
    - echo 'ignoring before_script'
  retry: *retry
  script:
    - if [ $BUILT_india != "true" ]; then echo "project india does not exists."; exit 1; fi;
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
    - echo "Attempting to install $INDIA_CONTAINER_REPO:r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID} to $CI_ENVIRONMENT_URL"
    - chmod 777 ./scripts/slugify.sh
    - export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
    - export CI_COMMIT_REF_SLUG_SLUGIFY=i-$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
    - echo helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/india/chart/india-next -f ././apps/india/chart/values/dev-sandbox.yaml
      --set "image.repository=${INDIA_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set "ingress.hosts[2]=${HOSTNAME_2}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/india/chart/india-next -f ././apps/india/chart/values/dev-sandbox.yaml
      --set "image.repository=${INDIA_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set "ingress.hosts[2]=${HOSTNAME_2}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_india_sandbox=true export DEPLOYED_india_sandbox
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/india-.*/
      when: on_success
    - when: manual
      allow_failure: true

undeploy:india-sandbox:
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  tags:
    - project-mihail
  when: manual
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.nxt.benzinga.dev
    GIT_STRATEGY: none
  environment:
    name: sandbox/india/${CI_COMMIT_REF_SLUG}
    url: https://${HOSTNAME}
    action: stop
  rules:
    - if: $DEPLOYED_india_sandbox == "true"
  before_script:
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  script:
    - |
      export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
      export CI_COMMIT_REF_SLUG_SLUGIFY=i-$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
      echo "Deleting helm deployment ${CI_COMMIT_REF_SLUG_SLUGIFY} ..."
      helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    # - kubectl delete namespace $KUBE_NAMESPACE || "Nothing to delete."

deploy:proto-beta:
  tags:
    - project-mihail
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  needs: ["image:proto"]
  dependencies: ["image:proto"]
  environment:
    name: production/proto/${CI_COMMIT_REF_SLUG}
    url: https://protobeta.benzinga.com
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
    HOSTNAME: protobeta.benzinga.com
  script:
    - if [ $BUILD_proto != "true" ]; then echo "project india does not exists."; exit 1; fi;
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
    - echo "Attempting to install $CI_REGISTRY_IMAGE/proto-next:r${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_URL}"
    - export CI_COMMIT_REF_SLUG_SLUGIFY="protobeta-master"
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/proto/chart/proto-next -f ././apps/proto/chart/values/dev-sandbox.yaml
      --set "image.repository=${PROTO_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_proto_beta=true export DEPLOYED_proto_beta
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success

deploy:proto-production:
  tags:
    - project-mihail
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  needs: ["image:proto"]
  dependencies: ["image:proto"]
  environment:
    name: production/proto/${CI_COMMIT_REF_SLUG}
    url: https://quotes.benzinga.com
  variables:
    <<: *global_variables
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - if [ $BUILD_proto != "true" ]; then echo "project proto does not exists."; exit 1; fi;
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
    - echo "Attempting to install $CI_REGISTRY_IMAGE/proto-next:r${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_URL}"
    - helm uninstall "${CI_COMMIT_REF_SLUG}" --namespace production || echo "No previous deployment for this branch."
    - helm install "${CI_COMMIT_REF_SLUG}" ./apps/proto/chart/proto-next -f ././apps/proto/chart/values/production.yaml --set "image.repository=${CI_REGISTRY_IMAGE}/benzinga-next" --set "image.tag=r${CI_COMMIT_SHA:0:8}-${CI_PIPELINE_ID}" --namespace production
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG}" --namespace production;
      echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - DEPLOYED_proto_production=true export DEPLOYED_proto_production
  retry: *retry
  rules:
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/proto\x40.*$/
      when: on_success

deploy:proto-static-production:
  <<: *aws_cli
  stage: deploy
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  script:
    # Use cp here to reset the object timestamp and expiration.  Lifecycle policy is 120days (currently).
    - echo "Syncing Next static assets to s3"
    - aws s3 cp ./dist/apps/proto/.next/static/ s3://bz-fusion-next-dotcom/_next/static/ --recursive --storage-class REDUCED_REDUNDANCY --metadata service=benzinga
    - DEPLOYED_proto_static_production=true export DEPLOYED_proto_static_production
  variables:
    <<: *global_variables
  tags:
    - project-mihail
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^release\/proto-.*/
      when: on_success
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/proto\x40.*$/
      when: on_success
    - when: manual
      allow_failure: true

deploy:proto-sandbox:
  allow_failure: true
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.proto.benzinga.dev
    HOSTNAME_1: ${CI_COMMIT_REF_SLUG}.protosbx.zingbot.bz
    HOSTNAME_2: ${CI_COMMIT_REF_SLUG}.protosbx.benzinga.com
  tags:
    - project-mihail
  environment:
    name: sandbox/proto/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.protosbx.benzinga.com
    auto_stop_in: 1 week
    on_stop: undeploy:proto-sandbox
  before_script:
    - echo 'ignoring before_script'
  retry: *retry

  needs: ["image:proto"]
  dependencies: ["image:proto"]
  script:
    - if [ $BUILD_proto != "true" ]; then echo "project proto does not exists."; exit 1; fi;
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
    - echo "Attempting to install $PROTO_CONTAINER_REPO:r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID} to $CI_ENVIRONMENT_URL"
    - chmod 777 ./scripts/slugify.sh
    - export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
    - export CI_COMMIT_REF_SLUG_SLUGIFY=p-$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
    - echo helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/proto/chart/proto-next -f ././apps/proto/chart/values/dev-sandbox.yaml
      --set "image.repository=${PROTO_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set "ingress.hosts[2]=${HOSTNAME_2}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/proto/chart/proto-next -f ././apps/proto/chart/values/dev-sandbox.yaml
      --set "image.repository=${PROTO_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set "ingress.hosts[2]=${HOSTNAME_2}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_proto_sandbox=true export DEPLOYED_proto_sandbox
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/(PROTO|proto)-.*/
      when: on_success
    - when: manual
      allow_failure: true

undeploy:proto-sandbox:
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  tags:
    - project-mihail
  when: manual
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.nxt.benzinga.dev
    GIT_STRATEGY: none
  environment:
    name: sandbox/proto/${CI_COMMIT_REF_SLUG}
    url: https://${HOSTNAME}
    action: stop
  rules:
    - if: $DEPLOYED_proto_sandbox == "true"
  before_script:
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  script:
    - |
      export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
      export CI_COMMIT_REF_SLUG_SLUGIFY=p-$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
      echo "Deleting helm deployment ${CI_COMMIT_REF_SLUG_SLUGIFY} ..."
      helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    # - kubectl delete namespace $KUBE_NAMESPACE || "Nothing to delete."

deploy:money-static-production:
  <<: *aws_cli
  stage: deploy
  needs:
    - job: build:changed-apps-projects
  dependencies:
    - build:changed-apps-projects
  script:
    # Use cp here to reset the object timestamp and expiration.  Lifecycle policy is 120days (currently).
    - if [ $BUILD_money != "true" ]; then echo "project bz does not exists."; exit 1; fi;
    - echo "Syncing Next static assets to s3"
    - aws s3 cp ./dist/apps/money/.next/static/ s3://bz-fusion-next-dotcom/money-build/_next/static/ --recursive --storage-class REDUCED_REDUNDANCY --metadata service=benzinga
    - DEPLOYED_money_static_production=true export DEPLOYED_money_static_production
  variables:
    <<: *global_variables
  tags:
    - project-mihail
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^release\/money-.*/
      when: on_success
    - when: manual
      allow_failure: true

deploy:money-sandbox:
  allow_failure: true
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.money.benzinga.dev
    HOSTNAME_1: ${CI_COMMIT_REF_SLUG}.moneysbx.zingbot.bz
    HOSTNAME_2: ${CI_COMMIT_REF_SLUG}.moneysbx.benzinga.com
  tags:
    - project-mihail
  environment:
    name: sandbox/money/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.moneysbx.benzinga.com
    auto_stop_in: 1 week
    on_stop: undeploy:money-sandbox
  before_script:
    - echo 'ignoring before_script'
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  script:
    - if [ $BUILD_money != "true" ]; then echo "project money does not exists."; exit 1; fi;
    - echo "Attempting to install $MONEY_CONTAINER_REPO:r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID} to $CI_ENVIRONMENT_URL"
    - chmod 777 ./scripts/slugify.sh
    - export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
    - export CI_COMMIT_REF_SLUG_SLUGIFY=m-$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
    - echo helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/money/chart/money-next -f ././apps/money/chart/values/dev-sandbox.yaml
      --set "image.repository=${MONEY_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set "ingress.hosts[2]=${HOSTNAME_2}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/money/chart/money-next -f ././apps/money/chart/values/dev-sandbox.yaml
      --set "image.repository=${MONEY_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set "ingress.hosts[1]=${HOSTNAME_1}"
      --set "ingress.hosts[2]=${HOSTNAME_2}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_money_sandbox=true export DEPLOYED_money_sandbox
  retry: *retry
  needs: ["image:money"]
  dependencies: ["image:money"]
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/money-.*/
      when: on_success
    - when: manual
      allow_failure: true

undeploy:money-sandbox:
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  tags:
    - project-mihail
  when: manual
  variables:
    <<: *global_variables
    HOSTNAME: ${CI_COMMIT_REF_SLUG}.nxt.benzinga.dev
    GIT_STRATEGY: none
  environment:
    name: sandbox/money/${CI_COMMIT_REF_SLUG}
    url: https://${HOSTNAME}
    action: stop
  rules:
    - if: $DEPLOYED_money_sandbox == "true"
  before_script:
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  script:
    - |
      export CI_COMMIT_REF_SLUG_TRUNCATED="${CI_COMMIT_REF_SLUG:0:50}"
      export CI_COMMIT_REF_SLUG_SLUGIFY=$(./scripts/slugify.sh ${CI_COMMIT_REF_SLUG_TRUNCATED})
      echo "Deleting helm deployment ${CI_COMMIT_REF_SLUG_SLUGIFY} ..."
      helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    # - kubectl delete namespace $KUBE_NAMESPACE || "Nothing to delete."
  retry: *retry

deploy:money-beta:
  allow_failure: true
  image: dtzar/helm-kubectl:3.2.4
  stage: deploy
  variables:
    <<: *global_variables
    HOSTNAME: betamoney.benzinga.com
  tags:
    - project-mihail
  environment:
    name: beta/money/${CI_COMMIT_REF_SLUG}
    url: https://betamoney.benzinga.com
  before_script:
    - echo 'ignoring before_script'
    - kubectl config set-credentials agent:$AGENT_ID --token="ci:${AGENT_ID}:${CI_JOB_TOKEN}"
    - kubectl config set-cluster gitlab --server="${K8S_PROXY_URL}"
    - kubectl config set-context "$KUBE_CONTEXT" --cluster=gitlab --user="agent:${AGENT_ID}"
    - kubectl config use-context "$KUBE_CONTEXT"
  script:
    - if [ ! -d "dist/apps/money" ]; then echo "project money does not exists."; exit 1; fi;
    - echo "Attempting to install $MONEY_CONTAINER_REPO:r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID} to $CI_ENVIRONMENT_URL"
    - export CI_COMMIT_REF_SLUG_SLUGIFY="moneybeta-master"
    - helm uninstall "${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE || echo "No such deployment found."
    - helm install "${CI_COMMIT_REF_SLUG_SLUGIFY}" ./apps/money/chart/money-next -f ././apps/money/chart/values/dev-sandbox.yaml
      --set "image.repository=${MONEY_CONTAINER_REPO}"
      --set "image.tag=r${CI_COMMIT_SHORT_SHA}-${CI_PIPELINE_ID}"
      --set "ingress.hosts[0]=${HOSTNAME}"
      --set annotations."app\.gitlab\.com/env"=$CI_ENVIRONMENT_SLUG
      --set annotations."app\.gitlab\.com/app"=$********************
      --namespace $KUBE_NAMESPACE
    - echo "Done. To delete this environment, run the following command:";
      echo "> helm uninstall ${CI_COMMIT_REF_SLUG_SLUGIFY}" --namespace $KUBE_NAMESPACE;
      echo "Deployed to $CI_ENVIRONMENT_URL"
    - DEPLOYED_moeny_beta=true export DEPLOYED_moeny_beta
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - when: manual
      allow_failure: true
  retry: *retry

# deploy:ui:storybook:
#   image: registry.gitlab.benzinga.io/benzinga/docker/python:3.9-slim-bullseye
#   stage: deploy
#   tags:
#     - project-mihail
#   environment:
#     name: ui/storybook/${CI_COMMIT_REF_SLUG}
#     url: ${STORYBOOK_CLOUDFRONT_URL}/${CI_COMMIT_REF_SLUG}/
#     on_stop: undeploy:ui-storybook
#   needs:
#     - build:changed-apps-projects
#   dependencies:
#     - build:changed-apps-projects
#   rules:
#     - /^\x40benzinga\/pro-frontend.*$/
#   rules:
#     - master
#     - /^bug\/.*/
#     - /^feature\/.*/
#     - /^hotfix\/.*/
#     - /^task\/.*/
#   before_script:
#     - pip install awscli
#   script:
#     - if [ ! -d "dist/storybook/ui-ui" ]; then echo "project storybook/ui-ui/ does not exists."; exit 1; fi;
#     # Uses AWS_ACCESS_KEY_ID & AWS_SECRET_ACCESS_KEY for arn:aws:iam::623948368456:user/Fusion-GitlabCI
#     - aws s3 rm s3://${STORYBOOK_S3_BUCKET}/${CI_COMMIT_REF_SLUG} --recursive || echo "Nothing to remove first." # remove all
#     - aws s3 sync ./dist/storybook/ui-ui/ s3://${STORYBOOK_S3_BUCKET}/${CI_COMMIT_REF_SLUG} --storage-class REDUCED_REDUNDANCY --metadata service=ui-storybook,surrogate-key=${CI_COMMIT_REF_SLUG},build=${CI_COMMIT_SHORT_SHA}
#     - aws cloudfront create-invalidation --distribution-id $STORYBOOK_CLOUDFRONT_ID --paths "/${CI_COMMIT_REF_SLUG}/*"
#     - echo "Deployed to ${STORYBOOK_CLOUDFRONT_URL}/${CI_COMMIT_REF_SLUG}/"
#   retry: *retry

deploy:newsdesk-tools-sandbox:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: sandbox/newsdesk/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.ndtsbx.zingbot.bz
    on_stop: "undeploy:newsdesk-tools-sandbox"
  variables:
    AWS_ROLE_ARN: $ROLE_ARN
    FASTLY_SERVICE_ID: 7EYJVqcBH1QDp1d9Bo1pAB
    FASTLY_PURGE_API_KEY: ${NEWSDESK_FASTLY_PURGE_API_KEY_SANDBOX}
  needs:
    - "build:changed-apps-projects"
  dependencies:
    - "build:changed-apps-projects"
  script:
    - if [ $BUILT_newsdesk_tools != "true" ]; then echo "project newsdesk-tools does not exists."; exit 1; fi;
    - echo CI_COMMIT_REF_SLUG = ${CI_COMMIT_REF_SLUG}
    - echo FASTLY_SERVICE_ID = ${FASTLY_SERVICE_ID}
    - aws s3 rm s3://bz-newsdesk-tools-v3-frontend-sandbox/${CI_COMMIT_REF_SLUG} --recursive || echo "Nothing to remove first." # remove all
    - aws s3 sync ./dist/apps/newsdesk-tools/ s3://bz-newsdesk-tools-v3-frontend-sandbox/${CI_COMMIT_REF_SLUG} --storage-class REDUCED_REDUNDANCY --metadata service=newsdesk-tools-v3,environment=sandbox,surrogate-key=${CI_COMMIT_REF_SLUG},build=${CI_COMMIT_REF_SLUG}
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - sleep 5
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty all POST "https://api.fastly.com/service/${FASTLY_SERVICE_ID}/purge/${CI_COMMIT_REF_SLUG}" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" 'Accept':'application/json'
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Newsdesk-Tools-v3-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
    - DEPLOYED_newsdesk_tools_sandbox=true export DEPLOYED_newsdesk_tools_sandbox
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/newsdesk-tools-.*/
      when: on_success
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/newsdesk-tools\x40.*$/
    - when: manual
      allow_failure: true

deploy:newsdesk-tools-staging:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: staging/newsdesk/${CI_COMMIT_REF_SLUG}
    url: https://newsdesk.zingbot.bz
  variables:
    AWS_ROLE_ARN: $ROLE_ARN
    FASTLY_SERVICE_ID: 53aJLWILZBoF2h7ciNgeOh
    FASTLY_PURGE_API_KEY: ${NEWSDESK_FASTLY_PURGE_API_KEY_STAGING}
  needs:
    - "build:changed-apps-projects"
  dependencies:
    - "build:changed-apps-projects"
  script:
    - if [ $BUILT_newsdesk_tools != "true" ]; then echo "project newsdesk-tools does not exists."; exit 1; fi;
    - export FASTLY_PURGE_API_KEY=${NEWSDESK_FASTLY_PURGE_API_KEY_STAGING}
    # bz-newsdesk-tools-v3-frontend-staging.s3-website-us-east-1.amazonaws.com
    - aws s3 rm s3://bz-newsdesk-tools-v3-frontend-staging/ --recursive # remove all
    - aws s3 sync ./dist/apps/newsdesk-tools/ s3://bz-newsdesk-tools-v3-frontend-staging/ --exact-timestamps --metadata service=newsdesk-tools-v3,environment=staging,surrogate-key=newsdesk-tools-v3-${CI_ENVIRONMENT_NAME},build=${CI_COMMIT_REF_SLUG}
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - sleep 5
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty all POST "https://api.fastly.com/service/${FASTLY_SERVICE_ID}/purge_all" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" 'Accept':'application/json'
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Newsdesk-Tools-v3-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
    - DEPLOYED_newsdesk_tools_staging=true export DEPLOYED_newsdesk_tools_staging
  retry: *retry
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - when: manual
      allow_failure: true

undeploy:newsdesk-tools-sandbox:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  when: manual
  environment:
    name: sandbox/newsdesk/${CI_COMMIT_REF_SLUG}
    url: https://${CI_COMMIT_REF_SLUG}.ndtsbx.zingbot.bz
    action: stop
  variables:
    AWS_ROLE_ARN: $ROLE_ARN
    GIT_STRATEGY: none # Dont pull in repo files
    FASTLY_SERVICE_ID: 7EYJVqcBH1QDp1d9Bo1pAB
    FASTLY_PURGE_API_KEY: ${NEWSDESK_FASTLY_PURGE_API_KEY_SANDBOX}
  rules:
    - if: $DEPLOYED_newsdesk_tools_sandbox == "true"
  script:
    - echo "Deleting S3 deployment newsdesk-tools-v3-frontend ${CI_COMMIT_REF_SLUG} ..."
    - aws s3 rm s3://bz-newsdesk-tools-v3-frontend-sandbox/${CI_COMMIT_REF_SLUG} --recursive
    - sleep 3
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty all POST "https://api.fastly.com/service/${FASTLY_SERVICE_ID}/purge/${CI_COMMIT_REF_SLUG}" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" 'Accept':'application/json'
  retry: *retry

deploy:newsdesk-tools-beta:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: beta/newsdesk/${CI_COMMIT_REF_SLUG}
    url: https://newsdeskbeta.benzinga.com
  variables:
    AWS_ROLE_ARN: $ROLE_ARN
    FASTLY_SERVICE_ID: 53aJLWILZBoF2h7ciNgeOh
    FASTLY_PURGE_API_KEY: ${NEWSDESK_FASTLY_PURGE_API_KEY_STAGING}
  needs:
    - "build:changed-apps-projects"
  dependencies:
    - "build:changed-apps-projects"
  script:
    - if [ $BUILT_newsdesk_tools != "true" ]; then echo "project newsdesk-tools does not exists."; exit 1; fi;
    - export FASTLY_PURGE_API_KEY=${NEWSDESK_FASTLY_PURGE_API_KEY_STAGING}
    # bz-newsdesk-tools-v3-frontend-staging.s3-website-us-east-1.amazonaws.com
    - aws s3 rm s3://bz-newsdesk-tools-v3-frontend-beta/ --recursive # remove all
    - aws s3 sync ./dist/apps/newsdesk-tools/ s3://bz-newsdesk-tools-v3-frontend-beta/ --exact-timestamps --metadata service=newsdesk-tools-v3,environment=staging,surrogate-key=newsdesk-tools-v3-${CI_ENVIRONMENT_NAME},build=${CI_COMMIT_REF_SLUG}
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - sleep 5
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty all POST "https://api.fastly.com/service/${FASTLY_SERVICE_ID}/purge_all" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" 'Accept':'application/json'
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Newsdesk-Tools-v3-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
    - DEPLOYED_newsdesk_tools_beta=true export DEPLOYED_newsdesk_tools_beta
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: on_success
    - when: manual
      allow_failure: true

deploy:newsdesk-tools-production:
  <<: *aws_cli
  stage: deploy
  tags:
    - project-mihail
  environment:
    name: production/newsdesk/${CI_COMMIT_REF_SLUG}
    url: https://newsdesk.benzinga.com
  variables:
    AWS_ROLE_ARN: $ROLE_ARN
    FASTLY_SERVICE_ID: 7XGkDtKhAmHKdIhNsHHhHi
    FASTLY_PURGE_API_KEY: ${NEWSDESK_FASTLY_PURGE_API_KEY_PRODUCTION}
  needs:
    - "build:changed-apps-projects"
  dependencies:
    - "build:changed-apps-projects"
  script:
    - if [ $BUILT_newsdesk_tools != "true" ]; then echo "project newsdesk-tools does not exists."; exit 1; fi;
    - export FASTLY_PURGE_API_KEY=${NEWSDESK_FASTLY_PURGE_API_KEY_PRODUCTION}
    # bz-newsdesk-tools-v3-frontend-production.s3-website-us-east-1.amazonaws.com
    # - aws s3 rm s3://bz-newsdesk-production/ --recursive # remove all
    - aws s3 sync ./dist/apps/newsdesk-tools/ s3://bz-newsdesk-production/ --exact-timestamps --metadata service=newsdesk-tools-v3,environment=production,surrogate-key=newsdesk-tools-v3-${CI_ENVIRONMENT_NAME},build=${CI_COMMIT_REF_SLUG}
    - echo "Deployed to ${CI_ENVIRONMENT_URL}"
    - sleep 5
    # Purge Fastly Cache - key allows purge access only to single service
    - http --verify=no --pretty all POST "https://api.fastly.com/service/${FASTLY_SERVICE_ID}/purge_all" 'Fastly-Key':"${FASTLY_PURGE_API_KEY}" 'Accept':'application/json'
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Newsdesk-Tools-v3-Frontend (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
    - DEPLOYED_newsdesk_tools_production=true export DEPLOYED_newsdesk_tools_production
  retry: *retry
  rules:
    - if: $CI_COMMIT_TAG =~ /^\x40benzinga\/newsdesk-tools.*$/
    - when: manual
      allow_failure: true

  # Undeploy

  # undeploy:ui-storybook:
  #   image: registry.gitlab.benzinga.io/benzinga/docker/python:3.9-slim-bullseye
  #   stage: deploy
  #   tags:
  #     - project-mihail
  #   when: manual
  allow_failure: true
#   variables:
#     <<: *global_variables
#     GIT_STRATEGY: none # Dont pull in repo files
#   environment:
#     name: ui/storybook/${CI_COMMIT_REF_SLUG}
#     url: ${STORYBOOK_CLOUDFRONT_URL}/${CI_COMMIT_REF_SLUG}/
#     action: stop
#   rules:
#     - /^bug\/.*/
#     - /^feature\/.*/
#     - /^hotfix\/.*/
#     - /^task\/.*/
#   before_script:
#     - pip install awscli httpie
#   script:
#     - |
#       echo "Deleting S3 deployment ui-storybook ${CI_COMMIT_REF_SLUG} ..."
#       aws s3 rm s3://${STORYBOOK_S3_BUCKET}/${CI_COMMIT_REF_SLUG}/ --recursive
#   retry: *retry

# Misc

cache clear next-quotes production:
  image: registry.gitlab.benzinga.io/benzinga/docker/python:3.9-slim-bullseye
  stage: deploy
  when: manual
  rules:
    - if: $CI_COMMIT_TAG == $CI_DEFAULT_BRANCH
  before_script:
    - pip install httpie
  script:
    - sleep 60
    # Purge next.benzinga.com and quotes.benzinga.com all
    - http --verify=no --pretty=all --json --ignore-stdin POST "https://api.fastly.com/service/55lTunyQPYLF3sH8aG7Sh9/purge_all" 'Fastly-Key':"${FASTLY_API_KEY}" 'Fastly-Soft-Purge':'1'
    # Soft purge Benzinga.com paths -- Skip static, _next as they should be unique assets
    - http --verify=no --pretty=all --json --ignore-stdin POST 'https://api.fastly.com/service/0z5TrWSVSY9b7N6xUdCNF5/purge' 'Fastly-Key':"${FASTLY_API_KEY}" 'Fastly-Soft-Purge':'1' 'Content-Type':'application/json; charset=utf-8' 'surrogate_keys':="[\"/calendars/*\",\"/quote/*\",\"/screener/*\",\"/tools/*\"]"
    # Send Confirmation to #dev-deployment Slack
    - http --verify=no --pretty=all --json --ignore-stdin POST "$SLACK_DEV_DEPLOYMENTS_WEBHOOK_URL" 'Content-Type':'application/json' text="Deployed *Fusion/Benzinga-Next (${CI_COMMIT_REF_SLUG})* at commit ${CI_COMMIT_SHA:0:8} to environment ${CI_ENVIRONMENT_NAME} -> ${CI_ENVIRONMENT_URL} and cleared Fastly cache. *Job Details* - ${CI_JOB_URL}"
  retry: *retry

# Publish Packages

publish:package:
  rules:
    - if: $CI_COMMIT_TAG =~ /\x40benzinga\/[a-z\-]+\/package\x40([0-9]+(\.[0-9]+){1,3})(-((beta|alpha)\.)?[0-9]+)?$/
  stage: deploy
  tags:
    - project-mihail
  dependencies:
    - build:changed-apps-projects
  # needs:
  #   - job: init-cache
  cache: *global_cache
  before_script:
    - "which ssh-agent || ( apt-get update -y && apt-get install openssh-client git -y )"
    - eval $(ssh-agent -s)
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
  script:
    - *bootstrap
    - export PACKAGE=$(echo ${CI_COMMIT_REF_NAME} | cut -d'/' -f 2)
    - echo ${PACKAGE}
    - yarn nx build tsmd
    - node dist/apps/tsmd/main.js render
    - pwd
    - cd $(cat ./workspace.json | python -c "import sys, json, os; print(json.load(sys.stdin)['projects'][os.environ.get('PACKAGE')]['targets']['build']['options']['outputPath'])" )
    - pwd
    - npm config set @benzinga:registry https://registry.npmjs.org
    - npm config set //registry.npmjs.org/:_authToken=$PUBLIC_NPM_AUTH_TOKEN
    - echo //registry.npmjs.org/:_authToken=${PUBLIC_NPM_AUTH_TOKEN} > .npmrc
    - cat .npmrc
    - npm config ls -l
    - if [[ "$CI_COMMIT_REF_NAME" == *"alpha"* ]]; then
      npm publish --tag alpha --access public;
      elif [[ "$CI_COMMIT_REF_NAME" == *"beta"* ]]; then
      npm publish --tag beta --access public;
      else
      npm publish --access public;
      fi
    - cd ../../../../
    - pwd
    - cat "${GITHUB_JAVASCRIPT_CLIENT_KEY}" > ~/.ssh/id_key
    - echo -e "\n" >> ~/.ssh/id_key
    - chmod 600 ~/.ssh/id_key
    - ssh-add ~/.ssh/id_key
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Benzinga SDK Bot"
    - GIT_SSH_COMMAND="ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no" node dist/apps/tsmd/main.js sync
  artifacts:
    name: "log files"
    untracked: false
    expire_in: 3 hours
    when: always
    paths:
      - /root/.npm/_logs/
  retry: *retry

# Automation Test
muuktest_automation:
  stage: test
  tags:
    - project-mihail
  image: node:20
  #image: registry.gitlab.benzinga.io/benzinga/docker/nikolaik/python-nodejs:python3.11-nodejs18
  rules:
    - if: $CI_COMMIT_BRANCH == "master"
      when: always
    - when: manual
      allow_failure: true
  before_script:
    - echo "Updating package list and installing core prerequisites..."
    - apt-get update -qq && apt-get install -yq curl unzip jq ffmpeg
    - echo "npm -v" && npm -v

  script:
    # Set -e ensures that the script exits immediately if any command fails.
    - set -e
    - |
      echo "--- Init Stage ---"
      mkdir -p executor
      cd executor || { echo "Failure: executor directory not found!"; exit 1; }
      # MUUKTEST_KEY should be passed as a GitLab CI/CD variables
      printf '{"key": "%s"}' "$MUUKTEST_KEY" > file.json
      # Print content for debugging/verification
      cat file.json
      # Resolve BASE_TEST_URL parameter: This variable should be defined in GitLab CI/CD variables 
      if [ -n "$BASE_TEST_URL" ]; then
        echo "Base URL: " "$BASE_TEST_URL";
        sed -i '/let baseurl =/a\  baseurl = `'$BASE_TEST_URL'`;' ./test/TestSteps_*.spec.ts;
      fi
      echo ""
      echo "--- Retrieve MuukTest config files and Test scripts ---"
      # Note: 'cd executor' is repeated here, which is fine, but if you have a very long script 
      # consider performing all actions from a single base directory or managing `cd` carefully. 
      curl -H "Content-Type: application/json" -X POST -d @file.json 'https://portal.muuktest.com:8081/generate_token_executer' -o "token.json" 
      curl -X POST https://portal.muuktest.com:8081/api/v1/downloadpwfiles -k -d @file.json -H "Content-Type: application/json" -o ./config.zip 
      unzip -o config.zip -d .
      MUUK1_TOKEN=$(jq --raw-output .token token.json)
      printf "Authorization: Bearer %s" "$MUUK1_TOKEN" > header.txt
      MUUK_USERID_TOKEN=$(jq --raw-output .userId token.json)

      # MUUKTEST_TAG_PROPERTY and MUUKTEST_TAG_VALUE should be passed as GitLab CI/CD variables 
      FIXED_TAG_VALUE="$MUUKTEST_TAG_VALUE"

      if [[ "$MUUKTEST_TAG_PROPERTY" == "hashtag" ]]; then
        [[ "$FIXED_TAG_VALUE" != "#"* ]] && FIXED_TAG_VALUE="#$FIXED_TAG_VALUE"
      fi
      printf '{"property": "%s", "value": ["%s"], "platform": "pw", "userId": "%s"}' "$MUUKTEST_TAG_PROPERTY" "$FIXED_TAG_VALUE" "$MUUK_USERID_TOKEN" > body.json
      curl -X POST https://portal.muuktest.com:8081/download_byproperty -H @header.txt -d @body.json -H "Content-Type: application/json" -o ./test.zip
      # Remove existing 'test' directory if it exists
      [ -d "./test" ] && rm -r test
      unzip -o test.zip -d ./test

      if [ -n "$BASE_TEST_URL" ]; then
        echo "Base URL: " "$BASE_TEST_URL";
        sed -i '/let baseurl =/a\  baseurl = `'$BASE_TEST_URL'`;' ./test/TestSteps_*.spec.ts;
      fi
      ls -l # List contents for verification
      echo "--- Installing PW and required packages ---"
      #npm install -D @playwright/test --force
      npm init playwright@latest -- --lang=ts --quiet --install-deps
      # MUUKTEST_BROWSER should be passed as a GitLab CI/CD variable
      
      npx playwright install --with-deps chromium
      npm install axios archiver cheerio xpath @xmldom/xmldom @faker-js/faker otpauth

      echo "--- Execute MuukTest E2E ---"
      npx playwright test --project=chromium

# Test for Fusion-6435

# build:test-6435:
#   needs: []
#   tags:
#     - project-mihail
#   rules:
#     - master
#     - merge_requests
#     - /^bug\/.*/
#     - /^feature\/.*/
#     - /^hotfix\/.*/
#     - /^release\/.*/
#     - /^task\/.*/
#     - /^\x40benzinga\/.*$/
#   before_script:
#     - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client git -y )'
#     - eval $(ssh-agent -s)
#     - mkdir -p ~/.ssh
#     - chmod 700 ~/.ssh
#   script:
#     - echo "${GITHUB_JAVASCRIPT_CLIENT_KEY}"
#     - cat "${GITHUB_JAVASCRIPT_CLIENT_KEY}" > ~/.ssh/id_key
#     - echo -e "\n" >> ~/.ssh/id_key
#     - chmod 600 ~/.ssh/id_key
#     - cat ~/.ssh/id_key
#     - ssh-add ~/.ssh/id_key
#     - echo "Done"
#   stage: build

## test

.test:bz-mobile: &test_bzm
  allow_failure: true
  stage: test
  image: node:18.19.1-alpine
  tags:
    - project-mihail
  cache:
    paths:
      - apps/bz-mobile/coverage/
  coverage: /All files[^|]*\|[^|]*\s+([\d\.]+)/
  dependencies:
    - lint_packages
  artifacts:
    name: "$CI_JOB_NAME"
    untracked: false
    expire_in: 3 hours
    when: always
    paths:
      - apps/bz-mobile/coverage/
    reports:
      coverage_report:
        coverage_format: cobertura
        path: apps/bz-mobile/coverage/cobertura-coverage.xml
  rules:
    - if: $CI_MERGE_REQUEST_SOURCE_BRANCH_NAME =~ /^.*\/bz_mobile-.*/
      when: on_success
    - when: manual
      allow_failure: true
  before_script:
    - if [ $BUILD_bz_mobile != "true" ]; then echo "project bz mobile does not exists."; exit 1; fi;
    - yarn install --silent --prefer-offline  --network-timeout 1000000000
  retry: *retry
# .test:pro: &test_pro
#   allow_failure: true
#   stage: test
#   image: cypress/browsers:node-18.16.1-chrome-114.0.5735.133-1-ff-114.0.2-edge-114.0.1823.51-1
#   tags:
#     - project-mihail
#   artifacts:
#     name: "$CI_JOB_NAME"
#     untracked: false
#     expire_in: 3 hours
#     when: always
#     paths:
#       - projects/tests/cypress/reports
#       - projects/tests/cypress/screenshots
#   rules:
#     # - /^bug\/.*/
#     # - /^feature\/.*/
#     # - /^hotfix\/.*/
#     - /^release\/PFE-.*/
#     # - /^task\/.*/
#     - master
#   needs:
#     - job: deploy:pro-staging
#       optional: true
#     - job: deploy:pro-sandbox-auto
#       optional: true
#     - job: deploy:pro-sandbox-manual
#       optional: true
#     - job: deploy:pro-production
#       optional: true
#     - job: deploy:pro-beta
#       optional: true
#   before_script:
#     - cd ./projects/tests
#     - yarn install --silent --prefer-offline  --network-timeout 1000000000
#     - export CYPRESS_baseUrl=https://${CI_COMMIT_REF_SLUG}.sbx.zingbot.bz/
#     - export CYPRESS_url=https://${CI_COMMIT_REF_SLUG}.sbx.zingbot.bz/
#     - export CYPRESS_prod_url=https://pro.benzinga.com/
#   retry: *retry

# test:pro:login:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/LoginTests.js"

# test:pro:chat:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/ChatTests.js"

# test:pro:calendar:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/CalendarTests.js"

# test:pro:signal:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/SignalTests.js"

# test:pro:checkout:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/CheckoutTests.js"

# test:pro:screener:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/ScreenerTests.js"

# test:pro:watchlist:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/WatchlistTest.js"

# test:pro:details:
#   <<: *test_pro
#   script: yarn run test:chrome -- --spec "cypress/integration/e2e/DetailsTests.js"

# .test:bzm:
#   <<: *test_bzm
#   script: NODE_OPTIONS=--max_old_space_size=65536 yarn nx test bz-mobile --configuration=ci

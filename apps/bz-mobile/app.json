{"expo": {"name": "Benzinga", "slug": "ben<PERSON>a", "scheme": "ben<PERSON>a", "owner": "ben<PERSON>a", "privacy": "unlisted", "sdkVersion": "51.0.34", "plugins": [["expo-notifications", {"icon": "./src/assets/images/notification_icon.png", "color": "#121115"}], "react-native-iap", "expo-asset", "expo-font", "expo-secure-store", ["expo-build-properties", {"android": {"minSdkVersion": 24}, "ios": {"useFrameworks": "static"}}], ["react-native-google-mobile-ads", {"androidAppId": "ca-app-pub-0540403401982416~4108351497", "iosAppId": "ca-app-pub-0540403401982416~6723421190", "skAdNetworkItems": ["cstr6suwn9.skadnetwork", "4fzdc2evr5.skadnetwork", "4pfyvq9l8r.skadnetwork", "2fnua5tdw4.skadnetwork", "ydx93a7ass.skadnetwork", "5a6flpkh64.skadnetwork", "p78axxw29g.skadnetwork", "v72qych5uu.skadnetwork", "ludvb6z3bs.skadnetwork", "cp8zw746q7.skadnetwork", "3sh42y64q3.skadnetwork", "c6k4g5qg8m.skadnetwork", "s39g8k73mm.skadnetwork", "3qy4746246.skadnetwork", "f38h382jlk.skadnetwork", "hs6bdukanm.skadnetwork", "v4nxqhlyqp.skadnetwork", "wzmmz9fp6w.skadnetwork", "yclnxrl5pm.skadnetwork", "t38b2kh725.skadnetwork", "7ug5zh24hu.skadnetwork", "gta9lk7p23.skadnetwork", "vutu7akeur.skadnetwork", "y5ghdn5j9k.skadnetwork", "n6fk4nfna4.skadnetwork", "v9wttpbfk9.skadnetwork", "n38lu8286q.skadnetwork", "47vhws6wlr.skadnetwork", "kbd757ywx3.skadnetwork", "9t245vhmpl.skadnetwork", "eh6m2bh4zr.skadnetwork", "a2p9lx4jpn.skadnetwork", "22mmun2rn5.skadnetwork", "4468km3ulz.skadnetwork", "2u9pt9hc89.skadnetwork", "8s468mfl3y.skadnetwork", "klf5c3l5u5.skadnetwork", "ppxm28t8ap.skadnetwork", "ecpz2srf59.skadnetwork", "uw77j35x4d.skadnetwork", "pwa73g5rt2.skadnetwork", "mlmmfzh3r3.skadnetwork", "578prtvx9j.skadnetwork", "4dzt52r2t5.skadnetwork", "e5fvkxwrpn.skadnetwork", "8c4e2ghe7u.skadnetwork", "zq492l623r.skadnetwork", "3rd42ekr43.skadnetwork", "3qcr597p9d.skadnetwork"], "userTrackingUsageDescription": "This identifier will be used to deliver personalized ads to you."}]], "platforms": ["ios", "android"], "jsEngine": "hermes", "userInterfaceStyle": "automatic", "version": "3.5.9", "orientation": "portrait", "icon": "./src/assets/images/icon.png", "splash": {"image": "./src/assets/images/splash-screen-dark-mode.png", "resizeMode": "contain", "backgroundColor": "#192940"}, "updates": {"fallbackToCacheTimeout": 60, "url": "https://u.expo.dev/8dd481f2-16c9-4c34-9778-1bfa3f27b238"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "icon": "./src/assets/images/icon.png", "bundleIdentifier": "com.Benzinga.BenzingaMobile", "buildNumber": "94", "usesAppleSignIn": true, "userInterfaceStyle": "automatic", "appStoreUrl": "https://apps.apple.com/in/app/benzinga-financial-news-data/id688949481", "googleServicesFile": "./firebase-ios/GoogleService-Info.plist", "bitcode": false, "config": {"usesNonExemptEncryption": false}}, "android": {"package": "com.benzinga.app", "versionCode": 77, "permissions": [], "googleServicesFile": "./firebase-android/google-services.json", "userInterfaceStyle": "automatic", "playStoreUrl": "https://play.google.com/store/apps/details?id=com.benzinga.app"}, "notification": {"iosDisplayInForeground": true}, "runtimeVersion": "exposdk:51.0.34", "extra": {"eas": {"projectId": "8dd481f2-16c9-4c34-9778-1bfa3f27b238"}}}, "react-native-google-mobile-ads": {"android_app_id": "ca-app-pub-0540403401982416~4108351497", "ios_app_id": "ca-app-pub-0540403401982416~6723421190", "sk_ad_network_items": ["cstr6suwn9.skadnetwork", "4fzdc2evr5.skadnetwork", "4pfyvq9l8r.skadnetwork", "2fnua5tdw4.skadnetwork", "ydx93a7ass.skadnetwork", "5a6flpkh64.skadnetwork", "p78axxw29g.skadnetwork", "v72qych5uu.skadnetwork", "ludvb6z3bs.skadnetwork", "cp8zw746q7.skadnetwork", "3sh42y64q3.skadnetwork", "c6k4g5qg8m.skadnetwork", "s39g8k73mm.skadnetwork", "3qy4746246.skadnetwork", "f38h382jlk.skadnetwork", "hs6bdukanm.skadnetwork", "v4nxqhlyqp.skadnetwork", "wzmmz9fp6w.skadnetwork", "yclnxrl5pm.skadnetwork", "t38b2kh725.skadnetwork", "7ug5zh24hu.skadnetwork", "gta9lk7p23.skadnetwork", "vutu7akeur.skadnetwork", "y5ghdn5j9k.skadnetwork", "n6fk4nfna4.skadnetwork", "v9wttpbfk9.skadnetwork", "n38lu8286q.skadnetwork", "47vhws6wlr.skadnetwork", "kbd757ywx3.skadnetwork", "9t245vhmpl.skadnetwork", "eh6m2bh4zr.skadnetwork", "a2p9lx4jpn.skadnetwork", "22mmun2rn5.skadnetwork", "4468km3ulz.skadnetwork", "2u9pt9hc89.skadnetwork", "8s468mfl3y.skadnetwork", "klf5c3l5u5.skadnetwork", "ppxm28t8ap.skadnetwork", "ecpz2srf59.skadnetwork", "uw77j35x4d.skadnetwork", "pwa73g5rt2.skadnetwork", "mlmmfzh3r3.skadnetwork", "578prtvx9j.skadnetwork", "4dzt52r2t5.skadnetwork", "e5fvkxwrpn.skadnetwork", "8c4e2ghe7u.skadnetwork", "zq492l623r.skadnetwork", "3rd42ekr43.skadnetwork", "3qcr597p9d.skadnetwork"], "user_tracking_usage_description": "This identifier will be used to deliver personalized ads to you."}}
{"build": {"common": {"node": "18.17.0", "yarn": "1.22.22", "android": {"withoutCredentials": true, "buildType": "apk"}, "ios": {"image": "latest", "fastlane": "2.206.2", "cocoapods": "1.11.3", "simulator": true}, "credentialsSource": "remote", "channel": "v3.5.9-stage"}, "production": {"extends": "common", "android": {"buildType": "apk"}, "ios": {"simulator": false}, "distribution": "store"}, "development": {"extends": "common", "developmentClient": true, "distribution": "internal", "android": {"buildType": "apk"}}, "devDevice": {"extends": "common", "developmentClient": false, "distribution": "internal", "ios": {"simulator": false}}, "preview": {"distribution": "internal", "developmentClient": false, "android": {"buildType": "apk"}, "ios": {"simulator": true}}}, "submit": {"production": {}}, "cli": {"appVersionSource": "local", "version": ">= 0.55.1"}}
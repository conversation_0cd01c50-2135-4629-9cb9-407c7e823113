import moment from 'moment';
import * as SecureStore from 'expo-secure-store';
import Data from '../data-mobile/data';
import { setNotificationChannel } from '../redux/actions/chat-action';
import UserData from './app';
import { Notification, NotificationHistoryResponse } from '@benzinga/notification-manager';
import * as TYPE from '../redux/actions/types';
import Constants from 'expo-constants';
import * as Updates from 'expo-updates';
import { Alert, PermissionsAndroid, Platform } from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { AnyAction, Dispatch } from 'redux';
import { NotificationRequest, NotificationRequestInput } from 'expo-notifications';
import { MainTabNavigatorStackParamList } from '../navigation/MainTabNavigator';
import { navigationRef } from './navigation';
import { PremiumIdeaFeed as PremiumFeed } from '@benzinga/trade-ideas-manager';
import { Permission, PermissionsManager } from '@benzinga/permission-manager';

export type ThemeType = 'System' | 'Dark' | 'Light';

export const getOtaVersion = () => {
  const otaVersion = process.env['REACT_NATIVE_EAS_BZ_VERSION'] || `v3.5.9.6`;
  const isStagingChannel = Updates.channel?.includes('stage') || false;
  if (!isStagingChannel) {
    return otaVersion.replace('.rc', '');
  }
  return otaVersion;
};

export const formatDate = (date: moment.MomentInput) => {
  const now = moment(date);
  const duration = moment.duration(now as moment.DurationInputArg1);
  const hours = duration.asHours();

  return hours <= 6 ? moment(date).fromNow() : moment(date).format('M/D/Y');
};

export function groupBy<T, K extends keyof T>(array: T[], key: K | { (obj: T): string }): Record<string, T[]> {
  const keyFn = key instanceof Function ? key : (obj: T) => obj[key];
  return array?.reduce(
    (objectsByKeyValue: Record<string, T[]>, obj) => {
      const value = keyFn(obj);
      objectsByKeyValue[value as string] = (objectsByKeyValue[value as string] || []).concat(obj);
      return objectsByKeyValue;
    },
    {} as Record<string, T[]>,
  );
}

export const getChannelListWithRealm = <T, K extends keyof T | ((obj: T) => string)>(
  array: T[],
  key: K | { (obj: T): string },
) => {
  const list = [];
  const allObject = groupBy(array, key);
  for (const key in allObject) {
    if (key.includes('-class')) {
      continue;
    }
    let customKey = key.replace(/-/g, ' ').replace('undefined', 'Others').replace('general', 'Chat Rooms');
    customKey = customKey.replace(/(^\w{1})|(\s+\w{1})/g, letter => letter.toUpperCase());
    list.push({ channels: allObject[key], title: customKey });
  }
  return list;
};

export const setItemToSecureStore = async (key: string, value: string) => {
  return SecureStore.setItemAsync(key, value);
};

export const getItemFromSecureStore = async (key: string) => {
  return SecureStore.getItemAsync(key);
};

export const removeItemFromSecureStore = async (key: string) => {
  return SecureStore.deleteItemAsync(key);
};

export const handleNotificationInteraction = (
  response: NotificationRequest | NotificationRequestInput | Notification,
  navigation: any,
  dispatch: Dispatch<AnyAction>,
) => {
  global.Analytics.event('NOTIFICATION CLICK', 'NOTIFICATION OPENS');

  const notificationData =
    (response as NotificationRequestInput)?.content?.data ||
    (response as Notification)?.data ||
    (response as NotificationRequest)?.trigger?.payload?.data;
  const isPriceAlert = notificationData?.uri?.includes('quotes') ? 'price_alert' : '';
  const screenToMove = notificationData?.screen || (notificationData as Notification['data'])?.metadata?.screen;
  const screenParams = notificationData?.params || (notificationData as Notification['data'])?.metadata?.params;
  const action =
    notificationData?.action ||
    notificationData?.metadata?.action ||
    notificationData?.type ||
    isPriceAlert ||
    response.type;

  if (notificationData?.notification_id) {
    readNotifications(dispatch, [notificationData.notification_id as number], undefined, 0);
  }

  Data.tracking().trackNotificationEvent('click', {
    notification_id: notificationData?.notification_id,
    notification_type: notificationData?.type,
  });

  const currentNavState = navigation.getParent()?.getState();
  const currentTab = currentNavState ? currentNavState.routeNames[currentNavState.index] : 'HomeTab';
  const currentScreen = navigationRef?.current?.getCurrentRoute()?.name ?? '';
  const isQuoteScreen = [
    'Profile',
    'Price Alerts',
    'Fundamentals',
    'Zingernation',
    'Analyst Ratings',
    'Earnings',
    'Dividends',
  ].includes(currentScreen);

  if (action) {
    switch (action) {
      case 'Open Chat': {
        (navigation as StackNavigationProp<MainTabNavigatorStackParamList>).navigate('ChatTab', {
          screen: 'ChannelListScreen',
        });
        if (dispatch) {
          dispatch(
            setNotificationChannel({
              screen: 'ChannelScreen',
              params: {
                ...screenParams,
                sourceTab: currentTab,
              },
            }),
          );
        }
        return;
      }
      case 'View Url':
        navigation.navigate('WebModalScreen', screenParams);
        return;

      case 'price_alert': {
        const symbol = notificationData?.dispatchPayload || notificationData?.metadata?.dispatchPayload;
        if (isQuoteScreen) {
          navigation.replace('Quote', { symbol });
        } else {
          navigation.navigate('Quote', { symbol });
        }
        return;
      }

      case 'trade_alert':
      case 'event_alert': {
        const params = { url: notificationData.uri };
        navigation.navigate('WebModalScreen', params);
        return;
      }
    }
  }

  if (screenToMove) {
    const { index, routes } = navigation.getState();
    const currentRoute = routes[index].name;
    if (currentRoute === screenToMove) {
      navigation.goBack();
    }

    if (screenToMove === 'ChannelScreen') {
      navigation.navigate('ChatTab', {
        screen: 'ChannelListScreen',
      });
      if (dispatch) {
        dispatch(
          setNotificationChannel({
            screen: 'ChannelScreen',
            params: {
              ...screenParams,
              sourceTab: currentTab,
            },
          }),
        );
      }
    } else {
      // Check and pop the screen from stack if user is already on the target screen
      const currentNavState = navigation?.getState();
      const mainTabNavState = currentNavState?.routes[0]?.state;
      const currentTabIndex = mainTabNavState?.index === undefined ? -1 : mainTabNavState?.index;
      if (currentTabIndex > -1) {
        // tab index found. This is null when user has recently started the app
        const selectedTabNavState = mainTabNavState?.routes[currentTabIndex]?.state;
        const selectedTabRoutes = selectedTabNavState?.routes;
        if (selectedTabRoutes) {
          const selectedLastRoute = selectedTabRoutes[selectedTabRoutes.length - 1];
          if (selectedLastRoute?.name === screenToMove) {
            navigation.goBack();
          }
        }
      }
      navigation.navigate(screenToMove, screenParams);
    }
  }
};

export const readNotifications = (
  dispatch: Dispatch<AnyAction>,
  readIds: number[],
  notificationData: NotificationHistoryResponse['data'] | undefined,
  index: number,
  readAll?: boolean,
) => {
  const bzUid = UserData.user()?.benzingaUid ?? '';
  const updatedNotificationResponse = Data.notifications().updateNotificationRead({
    bz_uid: bzUid?.toString() ?? '',
    read_ids: readIds,
    unread_ids: [],
    read_all: readAll ? true : false,
  });
  return updatedNotificationResponse
    .then(res => {
      if (!readAll && notificationData) {
        if (notificationData.notifications[index].read === true) {
          dispatch({
            type: TYPE.NOTIFICATION_HISTORY_SUCCESS,
            data: {
              ...notificationData,
              unread_count: notificationData?.unread_count,
              notifications: [...notificationData.notifications],
            },
          });
        } else {
          notificationData.notifications[index].read = true;
          dispatch({
            type: TYPE.NOTIFICATION_HISTORY_SUCCESS,
            data: {
              ...notificationData,
              unread_count: notificationData?.unread_count - 1,
              notifications: [...notificationData.notifications],
            },
          });
        }
      }
      return res;
    })
    .catch(err => {
      console.log('Err while updating read count of notification: ', err);
      return err;
    });
};

export const unreadNotifications = (
  dispatch: Dispatch<AnyAction>,
  unreadIds: number[],
  notificationData: NotificationHistoryResponse['data'],
  index: number,
) => {
  const bzUid = UserData.user()?.benzingaUid ?? '';
  const updatedNotificationResponse = Data.notifications().updateNotificationRead({
    bz_uid: bzUid?.toString() ?? '',
    read_ids: [],
    unread_ids: unreadIds,
  });

  return updatedNotificationResponse
    .then(res => {
      notificationData.notifications[index].read = false;
      dispatch({
        type: TYPE.NOTIFICATION_HISTORY_SUCCESS,
        data: {
          ...notificationData,
          unread_count: notificationData?.unread_count + 1,
          notifications: [...notificationData.notifications],
        },
      });
      return res;
    })
    .catch(err => {
      console.log('Err while updating unread count of notification: ', err);
      return err;
    });
};

export const storeUrl = () => {
  const { manifest, manifest2 } = Constants;
  if (Platform.OS === 'ios' && manifest?.ios) {
    return manifest.ios.appStoreUrl ?? null;
  } else if (Platform.OS === 'ios' && manifest2?.extra?.expoClient?.ios) {
    return manifest2.extra.expoClient.ios.appStoreUrl ?? null;
  } else if (Platform.OS === 'android' && manifest?.android) {
    return manifest.android.playStoreUrl ?? null;
  } else if (Platform.OS === 'android' && manifest2?.extra?.expoClient?.android) {
    return manifest2.extra.expoClient.android.playStoreUrl ?? null;
  }
  return null;
};

export const getUserThemePreference = async () => {
  return await Data.user().getGlobalSettings();
};

export const setUserThemePreference = async (theme: ThemeType) => {
  return Data.user().setGlobalSetting('preferred_theme', theme);
};

export const validateEmail = (email: string) => {
  const re =
    /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
  return re.test(String(email).toLowerCase());
};

export const initializeConfiguration = () => {
  if (!__DEV__) {
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    console.log = () => {};
  }
};

export const requestNotificationPermission = async () => {
  if (Platform.OS === 'android' && Platform.Version >= 33) {
    try {
      const granted = await PermissionsAndroid.request(PermissionsAndroid.PERMISSIONS.POST_NOTIFICATIONS);
      if (granted !== PermissionsAndroid.RESULTS.GRANTED) {
        Alert.alert('You have denied notification permission. You may not receive important notifications.');
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
    }
  }
};

export const checkMavenTradesUserPremium = async () => {
  const ideasFeed = Data.tradeIdeas().premiumFeed(
    {
      limit: 10,
    },
    'stock | ticker | option',
  );
  const hasPremium = await hasSubscription(ideasFeed);
  return hasPremium;
};

export const hasSubscription = async (ideasFeed: PremiumFeed): Promise<boolean | undefined> => {
  let hasPremium = false;
  const expertsArray = await ideasFeed.getExpertsArray();
  return expertsArray.some(item => {
    if (!item.locked) {
      const hasPackageIds = Boolean(item.package_ids.length);
      hasPremium = hasPackageIds;
    }
    return hasPremium;
  });
};

export const hasProAccess = (
  permissions: Permission[],
  permissionsManager: PermissionsManager,
  type: 'and' | 'or' = 'and',
): boolean => {
  const operation = (perm: boolean | Permission) =>
    typeof perm === 'boolean' ? perm : permissionsManager.hasAccess(perm.action, perm.resource).ok ?? false;
  return type === 'and' ? permissions.every(operation) : permissions.some(operation);
};

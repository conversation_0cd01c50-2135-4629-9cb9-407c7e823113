import React from 'react';
import { Metadata } from 'next';
import { AntdRegistry } from '@ant-design/nextjs-registry';
import dayjs from 'dayjs';

import { getInsiderTradesNetWorth } from '../../data';
import { BuyAndBrokerButtons, AttentionProCTA } from '../../../_components';
import { InsiderLineChart } from '../../../_components/LineChart';
import { InsiderTradesFilingTable } from '../../../_components/InsiderTradesFilingTable';
import { toTitleCase } from '@benzinga/utils';
import { BENZINGA_LOGO_URL } from '@benzinga/seo';

export const generateMetadata = async ({ params }) => {
  const { insider_cik, name } = await params;
  const sanitizedName = name.replaceAll(/[^a-zA-Z0-9 ]/g, ' ');
  const insiderName = toTitleCase(sanitizedName);

  const title = name
    ? `${insiderName} Net Worth - Insider Trades and Bio as of ${dayjs().format('MMM D, YYYY')}`
    : 'Benzinga Net Worth and Insider Trades';
  const canonical = 'https://www.benzinga.com/sec/insider-trades/' + insider_cik + '/' + name;
  const description = (name ? insiderName : 'Insider') + ' trade filings from the SEC.';

  const metadata: Metadata = {
    alternates: {
      canonical,
    },
    authors: [{ name: 'Benzinga' }],
    description,
    openGraph: {
      description,
      images: [{ alt: 'Benzinga Logo', height: 300, url: BENZINGA_LOGO_URL, width: 400 }],
      title,
      type: 'website',
      url: canonical,
    },
    title,
  };

  return metadata;
};

export default async function Page({ params }) {
  const { insider_cik } = await params;
  const data = await getInsiderTradesNetWorth(insider_cik);

  const insider = data?.insider;
  const groupedFilings = data?.insider?.filings?.reduce((acc: any, filing: any) => {
    if (!acc[filing.company_cik]) {
      acc[filing.company_cik] = [];
    }
    acc[filing.company_cik].push(filing);
    return acc;
  }, {});

  if (!insider) {
    return <div>No Insider Found</div>;
  }

  return (
    <div className="insider-layout net-worth-page">
      <div className="flex flex-col gap-2 border-b-2 border-black pb-4 mb-4">
        <div className="flex flex-col-reverse md:flex-row justify-between">
          <h1>{insider?.name}'s Net Worth </h1>
          <BuyAndBrokerButtons />
        </div>
        <div className="text-7xl font-bold">{insider?.net_worth_string}</div>
        <p className="text-base font-bold">
          Estimate Recalculated {dayjs(insider?.updated_at ?? '').format('MMM D, YYYY hh:mmA')} EST
        </p>
      </div>
      <div className="flex flex-col gap-4 mb-2">
        <div>
          <h2>Who is {insider?.alias}</h2>
          <p className="text-sm" dangerouslySetInnerHTML={{ __html: insider?.trades_overview_string }}></p>
        </div>
        <div>
          <h2>SEC CIK</h2>
          <p className="text-sm">
            {insider?.alias}'s CIK is <strong>{insider?.cik}</strong>
          </p>
        </div>
        <div>
          <h2>Past Insider Trading and Trends</h2>
          <p className="text-sm" dangerouslySetInnerHTML={{ __html: insider?.trades_overall_string }}></p>
        </div>
      </div>
      <div className="">
        <InsiderLineChart {...insider?.line_chart} />
      </div>
      <div className="text-sm my-4">
        <AttentionProCTA />
      </div>
      <AntdRegistry>
        <div className="flex flex-col gap-4">
          {insider?.companies?.length > 0 &&
            insider?.companies?.map((company, index) => {
              return (
                <InsiderTradesFilingTable
                  {...company}
                  calendarSlug={'insider-trades-networth'}
                  filings={groupedFilings[company.cik] || []}
                  id={company.id}
                  hideDatePicker
                  key={index}
                  tableIndex={index}
                  title={company.name}
                />
              );
            })}
        </div>
      </AntdRegistry>
    </div>
  );
}

import React from 'react';
import Link from 'next/link';

import { getInsiderTradesDefault } from './data';
import { BuyAndBrokerButtons, InsiderTradesFAQs, AttentionProCTA } from '../_components';
import { InsiderTradesFilingTable } from '../_components/InsiderTradesFilingTable';
import { BENZINGA_LOGO_URL } from '@benzinga/seo';
import { Metadata } from 'next';

const title = 'Insider Trades - Track Insider Buying & Selling Today - Benzinga';
const description = 'SEC filings and insider trades Form 4 filings from the SEC';
const canonical = 'https://www.benzinga.com/sec/insider-trades';

export const metadata: Metadata = {
  alternates: {
    canonical,
  },
  authors: [{ name: '<PERSON><PERSON>' }],
  description,
  openGraph: {
    description,
    images: [{ alt: 'Benzinga Logo', height: 300, url: BENZINGA_LOGO_URL, width: 400 }],
    title,
    type: 'website',
    url: canonical,
  },
  title,
};

export default async function Page() {
  const data = await getInsiderTradesDefault();

  const quickLinks = data?.map(preset => {
    return {
      title: preset.title,
      url: `/sec/insider-trades#${preset.id}`,
    };
  });

  return (
    <div className="insider-layout">
      <div className="flex flex-col gap-2">
        <div className="flex flex-row justify-between">
          <h1>Insider Trades</h1>
          <BuyAndBrokerButtons />
        </div>
        <p>
          Learning where the insiders are buying and selling is just as important as building a trading strategy. When
          the insiders are buying and selling shares of their own companies (or even parallel businesses), you can learn
          where the money is going.
        </p>
        <p>
          Take a look at this live tracker from Benzinga that shows you where insider cash is moving, allowing you to
          make wise decisions for your own portfolio. Remember, this is not the same as copy trading. Insiders don’t
          intend for you to copy their trades, but you can glean quite a lot of information from their activity.
        </p>
        <AttentionProCTA />
      </div>
      <div className="flex flex-col md:flex-row gap-4 my-4">
        {quickLinks?.length > 0 &&
          quickLinks.map(link => {
            return (
              <Link href={link.url} key={link.title}>
                {link.title}
              </Link>
            );
          })}
      </div>
      <div className="flex flex-col gap-4">
        {data?.length > 0 &&
          data?.map((preset, index) => {
            return (
              <InsiderTradesFilingTable
                {...preset}
                hideDatePicker
                id={preset.id}
                key={index}
                showAllButton
                isGated={false}
              />
            );
          })}
      </div>
      <InsiderTradesFAQs />
    </div>
  );
}

import { ArticleManager } from '@benzinga/article-manager';
import { NextApiRequest, NextApiResponse } from 'next';
import { getGlobalSession } from './session';

interface QuestionsApiResponse {
  article_index: string;
  created_at: string;
  follow_up_questions: string[];
  node_id: string;
  post_id: number;
  status: string;
}

interface ErrorResponse {
  error: {
    message: string;
  };
}

const handler = async (req: NextApiRequest, res: NextApiResponse<QuestionsApiResponse | ErrorResponse>) => {
  const { articleId } = req.query;

  if (req.method !== 'GET') {
    return res.status(405).json({
      error: { message: 'Method not allowed' },
    });
  }

  if (!articleId || typeof articleId !== 'string' || !/^\d+$/.test(articleId)) {
    return res.status(400).json({
      error: {
        message: 'Article ID must be a valid number',
      },
    });
  }

  try {
    const session = getGlobalSession();
    const articleManager = session.getManager(ArticleManager);
    const response = await articleManager.getFollowUpQuestionsInternal(articleId);

    if (response.err) {
      return res.status(500).json({
        error: {
          message: 'Failed to fetch followup questions',
        },
      });
    }

    if (!response.ok) {
      return res.status(404).json({
        error: {
          message: 'No results found',
        },
      });
    }

    return res.status(200).json(response.ok);
  } catch (error) {
    console.error('Error fetching WNSTN followup questions:', error);
    return res.status(500).json({
      error: {
        message: 'Internal server error',
      },
    });
  }
};

export default handler;

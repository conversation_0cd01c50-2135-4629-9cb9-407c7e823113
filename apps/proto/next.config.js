//@ts-check

// eslint-disable-next-line @typescript-eslint/no-var-requires
const { composePlugins, withNx } = require('@nx/next');

const envData = require('./public/env/index.json');
const RELEASE_VERSION = require('./package.json')?.version;
const { GitRevisionPlugin } = require('git-revision-webpack-plugin');
const gitRevisionPlugin = new GitRevisionPlugin();

const env = {
  ...(envData ?? {}),
  ...(process.env.NODE_ENV === 'development' && {
    RUNTIME_ENV: 'development',
  }),
  GIT_COMMITHASH: process.env.CI_COMMIT_SHA ?? gitRevisionPlugin.commithash() ?? '',
  RELEASE_VERSION,
};

/**
 * @type {import('@nx/next/plugins/with-nx').WithNxOptions}
 **/
const nextConfig = {
  env,
  // RULE: Last match WINS
  async headers() {
    // Benzinga-Control - Same as Benzinga TTL for legacy reasons, but we can probably remove
    // Benzinga-TTL - Sets the cache TTL for the returned object
    // Benzinga-stale_while_revalidate - Sets max_stale_while_revalidate, or how long the object should be served while stale
    // Benzinga-stale_if_error - If there are errors, how long can we serve the stale object

    return [
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=300',
          },
          {
            key: 'Benzinga-TTL',
            value: '600s',
          },
        ],
        // 10m Default
        source: '/:path*',
      },
      {
        headers: [
          {
            key: 'Content-Security-Policy',
            value: '*',
          },
        ],
        source: '/widgets/:path*',
      },
      //w/widget pages
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=86400',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        source: '/w/:path*',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Briefs, 60s
        source: '/briefs',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Recent, 60s
        source: '/recent',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Recent, 60s
        source: '/exclusives',
      },
      {
        headers: [
          {
            key: 'Benzinga-Control',
            value: 'max-age=60',
          },
          {
            key: 'Benzinga-TTL',
            value: '60s',
          },
          {
            key: 'Benzinga-stale_while_revalidate',
            value: '30s',
          },
        ],
        // Watch, 60s
        source: '/watch',
      },
    ];
  },
  // assetPrefix: '/proto-build',
  images: {
    dangerouslyAllowSVG: true,
    domains: ['image-util.benzinga.com', 'assets.coingecko.com', 'cdn.benzinga.com'],
  },

  nx: {
    svgr: false,
  },
  async rewrites() {
    return [
      {
        destination: '/api/embed/:path*',
        source: '/embed/api/:path*',
      },
    ];
  },
  transpilePackages: [
    '@ant-design',
    'antd',
    '@coralogix/browser',
    '@ant-design/icons',
    '@ant-design/icons-svg',
    '@ant-design/v5-patch-for-react-19',
    'rc-util',
    'rc-pagination',
    'rc-picker',
    'rc-picker/lib/generate/dayjs',
    'rc-picker/lib/generate',
    'rc-picker/lib',
    'rc-table',
    'rc-tree',
    'react-tweet',
    "rc-cascader",
    "rc-checkbox",
    "rc-collapse",
    "rc-component",
    "@rc-component",
    "@rc-component/util",
    "rc-component/util",
    "rc-dialog",
    "rc-drawer",
    "rc-dropdown",
    "rc-field-form",
    "rc-image",
    "rc-input",
    "rc-input-number",
    "rc-mentions",
    "rc-menu",
    "rc-motion",
    "rc-notification",
    "rc-pagination",
    "rc-picker",
    "rc-progress",
    "rc-rate",
    "rc-resize-observer",
    "rc-segmented",
    "rc-select",
    "rc-slider",
    "rc-steps",
    "rc-switch",
    "rc-table",
    "rc-tabs",
    "rc-textarea",
    "rc-tooltip",
    "rc-tree",
    "rc-tree-select",
    "rc-upload",
    "rc-util",
  ],
  webpack: config => {
    // Grab the existing rule that handles SVG imports
    const fileLoaderRule = config.module.rules.find(rule => rule.test?.test?.('.svg'));

    config.module.rules.push(
      // Reapply the existing rule, but only for svg imports ending in ?url
      {
        ...fileLoaderRule,
        resourceQuery: /url/,
        test: /\.svg$/i, // *.svg?url
      },
      // Convert all other *.svg imports to React components
      {
        issuer: fileLoaderRule.issuer,
        resourceQuery: { not: [...fileLoaderRule.resourceQuery.not, /url/] },
        test: /\.svg$/i, // exclude if *.svg?url
        use: ['@svgr/webpack', 'file-loader'],
      },
    );

    // Modify the file loader rule to ignore *.svg, since we have it handled now.
    fileLoaderRule.exclude = /\.svg$/i;

    return config;
  },
};

const plugins = [
  // Add more Next.js plugins to this list if needed.
  withNx,
];

module.exports = composePlugins(...plugins)(nextConfig);

import React, { useState } from 'react';
import type { AppProps } from 'next/app';

import { GoogleTagAnalytics } from '@benzinga/analytics';

import { ThemeProvider, StyleSheetManager } from '@benzinga/themetron';

import { getGlobalSessionSingleton } from './api/session';
import { SessionContextProvider } from '@benzinga/session-context';
import { useUser, useUserSubscriptions } from '@benzinga/user-context';

import { OwnProps as HeaderProps } from '../src/layouts/Header';
import { TrackingManager } from '@benzinga/tracking-manager';

// Global Styles
import '@benzinga/globalStyles';
// THIRD PARTY STYLES
import '@ag-grid-community/styles/ag-grid.css';
import '@ag-grid-community/styles/ag-theme-alpine.css';

import Page from '../src/components/Page';
import { useRouter } from 'next/router';

import '@iframe-resizer/child';

interface Props extends AppProps {
  headerProps: HeaderProps;
}

export const StoreContext = React.createContext(null);

export const MyApp: React.FC<Props> = ({ Component, pageProps }) => {
  const session = getGlobalSessionSingleton();
  const user = useUser();
  const subscriptions = useUserSubscriptions();
  const router = useRouter();

  React.useEffect(() => {
    session?.getManager(TrackingManager).setMeta(pageProps?.metaProps || null);
  }, [session, pageProps?.metaProps]);

  React.useEffect(() => {
    session?.getManager(TrackingManager).setUser(user);
  }, [session, user]);

  React.useEffect(() => {
    session?.getManager(TrackingManager).setSubscriptions(subscriptions);
  }, [session, subscriptions]);

  React.useEffect(() => {
    if (!(window as any).gtagAnalytics) {
      let measurementId = process.env.GOOGLE_TAG_MANAGER_MEASUREMENT_ID as string;
      if (router.asPath?.includes('/embed')) {
        measurementId = process.env.GOOGLE_TAG_MANAGER_MEASUREMENT_ID_EMBED_WIDGETS as string;
      } else if (router.asPath?.includes('embedded=true') && router.asPath?.includes('/widgets/')) {
        measurementId = process.env.GOOGLE_TAG_MANAGER_MEASUREMENT_ID_WIDGETS as string;
      }

      GoogleTagAnalytics.initialize({
        logger: true,
        measurementId,
      });
    }
  }, [router.asPath]);

  return (
    <SessionContextProvider session={session}>
      <StyleSheetManager>
        <ThemeProvider theme="modern">
          <React.Suspense>
            <Page {...pageProps}>
              <Component {...pageProps} />
            </Page>
          </React.Suspense>
        </ThemeProvider>
      </StyleSheetManager>
    </SessionContextProvider>
  );
};

export default MyApp;

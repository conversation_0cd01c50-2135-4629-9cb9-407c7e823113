import { NextApiRequest, NextApiResponse } from 'next';
import { getGlobalSession } from './session';
import { ContentManager } from '@benzinga/content-manager';
import { safeErrorStatus } from '@benzinga/safe-await';
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  try {
    const session = getGlobalSession();
    const { path } = req.query;

    const postResponse = await session.getManager(ContentManager).getWordpressPost(path as string);
    const postData = postResponse.ok;

    const responseCode = (postResponse?.err?.data as safeErrorStatus)?.status;
    if (responseCode && responseCode >= 400 && responseCode < 600 && postResponse?.ok === undefined) {
      return res.status(responseCode).json({ error: `Something went wrong!` });
    }

    if (!postData || !Array.isArray(postData?.blocks)) {
      return res.status(404).json({ error: 'Widget not found' });
    }

    res.setHeader('Cache-Control', 'public, max-age=86400, s-maxage=86400'); // Cache for 1 day
    res.setHeader('Content-Type', 'application/json');
    return res.status(200).json(postData);
  } catch (error) {
    console.error('Widget API Error:', error);
    return res.status(500).json({ error: `Widget API Error: ${error}` });
  }
}

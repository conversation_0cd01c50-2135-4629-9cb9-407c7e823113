import React from 'react';
import { GetServerSideProps, NextPage } from 'next';
import Error from '../_error';
import { ContentManager, WordpressWidget } from '@benzinga/content-manager';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { getGlobalSession } from '../api/session';
import { MoneyWidget, PageProps, AffiliateContext, AffiliateProvider } from '@benzinga/money';
import { ResizeDetector } from '@benzinga/core-ui';
import { safeErrorStatus } from '@benzinga/safe-await';

interface WidgetPageProps extends PageProps {
  aff_id?: string;
  widget: WordpressWidget;
  widgetId?: string;
}

const Page: NextPage<WidgetPageProps> = props => {
  const { setAffiliateId } = React.useContext(AffiliateContext);

  if (props.error) {
    return (<Error statusCode={404} />) as React.ReactElement;
  }

  if (props.widget?.type === 'widget') {
    if (props.aff_id) {
      setAffiliateId(props.aff_id);
    }
    return (
      <div className="widget-wrap" data-iframe-height>
        <ResizeDetector widget widgetId={props.widgetId}>
          <AffiliateProvider>
            <MoneyWidget hideAdminBar={true} widget={props.widget} />
          </AffiliateProvider>
        </ResizeDetector>
      </div>
    );
  }

  return (<Error statusCode={404} />) as React.ReactElement;
};

export const getServerSideProps: GetServerSideProps = async ({ query, res, resolvedUrl }) => {
  const headerProps = {
    hideQuicklinks: true,
    hideAdminBar: true,
    hideNavigationBar: true,
    hideTopBar: true,
    hideSearchBar: true,
    hideheader: true,
    hidefooter: true,
    hideMenuBar: true,
    disableRaptiveReadyOnPageLoad: true,
    hideMobileAppBannerAd: true,
    showRotatingBanner: false,
    showRaptiveBanner: false,
    shouldRenderRaptiveBanner: false,
    disableOptinMonster: true,
    hideBanner: true,
    hideFooter: true,
    hideQuoteBar: true,
  };
  try {
    const session = getGlobalSession();

    const postResponse = await session.getManager(ContentManager).getMoneyWidget(query.w as string);
    const postData = postResponse.ok;

    const responseCode = (postResponse?.err?.data as safeErrorStatus)?.status;

    if (responseCode && responseCode >= 400 && responseCode < 600 && postResponse?.ok === undefined) {
      res.statusCode = responseCode;
      return {
        props: {
          headerProps: headerProps,
          metaProps: { robots: 'noindex, nofollow' },
          error: [404, 410].includes(responseCode) ? responseCode : 503,
          news: [],
          topNews: [],
          topic: '',
        },
      };
    }

    if (Array.isArray(postData?.blocks) && postData.success !== false) {
      postData.blocks = await loadServerSideBlockData(session, postData.blocks);

      return {
        props: {
          aff_id: query?.aff_id ?? null,
          disablePageTracking: query?.embedded ?? false,
          embeddedWidget: query?.embedded ?? false,
          headerProps: headerProps,
          metaProps: { robots: 'noindex, nofollow' },
          resolvedUrl,
          widget: postData,
          widgetId: query?.widget_id ?? null,
        },
      };
    }
    res.statusCode = 404;
    return {
      props: {
        aff_id: null,
        error: 404,
        news: [],
        topNews: [],
        topic: '',
      },
    };
  } catch {
    res.statusCode = 404;
    return {
      props: {
        aff_id: null,
        error: 404,
        headerProps: headerProps,
        news: [],
        topNews: [],
        topic: '',
      },
    };
  }
};

export default Page;

import React from 'react';
import { GetServerSideProps, NextPage } from 'next';

import Error from '../_error';
import { ContentManager, WordpressWidget } from '@benzinga/content-manager';
import { loadServerSideBlockData } from '@benzinga/blocks-utils';
import { getGlobalSession } from '../api/session';
import { MoneyWidget, PageProps, AffiliateContext, AffiliateProvider } from '@benzinga/money';
import styled, { TC } from '@benzinga/themetron';
import { ResizeDetector } from '@benzinga/core-ui';
import { BenzingaLogo } from '@benzinga/logos-ui';
import WidgetConfig from '../../src/components/WidgetConfig';
import { getProperWidgetName } from './index';

interface WidgetPageProps extends PageProps {
  aff_id?: string;
  widget: WordpressWidget;
  widgetId?: string;
  embeddedWidget?: boolean;
  resolvedUrl?: string;
}

const Page: NextPage<WidgetPageProps> = props => {
  const { setAffiliateId } = React.useContext(AffiliateContext);
  const [autosize, setAutosize] = React.useState(true);
  const [widgetHeight, setWidgetHeight] = React.useState('1080');
  const [widgetWidth, setWidgetWidth] = React.useState('1080');

  if (props.error) {
    return (<Error statusCode={404} />) as React.ReactElement;
  }

  if (props.widget?.type === 'widget') {
    if (props.embeddedWidget) {
      if (props.aff_id) {
        setAffiliateId(props.aff_id);
      }
      return (
        <ResizeDetector widget widgetId={props.widgetId}>
          <AffiliateProvider>
            <MoneyWidget widget={props.widget} />
          </AffiliateProvider>
          <a href="https://www.benzinga.com" rel="noreferrer" target="_blank">
            <ProvidedByBenzinga>
              Provided by <BenzingaLogo variant="dark" />
            </ProvidedByBenzinga>
          </a>
        </ResizeDetector>
      );
    } else {
      return (
        <TC.Row>
          <div
            className="grid grid-cols-12 grid-flow-row grid-rows-1"
            style={{ margin: 'auto', maxWidth: '1280px', width: '100%' }}
          >
            <div className="lg:col-span-12 md:col-span-12 col-span-12">
              <MoneyWidgetContainer>
                <MoneyWidget widget={props.widget} />
              </MoneyWidgetContainer>
            </div>
            <div className="lg:col-span-12 md:col-span-12 col-span-12">
              <WidgetConfig
                autosize={autosize}
                setParentAutosize={setAutosize}
                setWidgetHeight={setWidgetHeight}
                setWidgetWidth={setWidgetWidth}
                widgetHeight={widgetHeight}
                widgetName={`${getProperWidgetName(props?.resolvedUrl ?? '')}`}
                widgetWidth={widgetWidth}
              />
            </div>
          </div>
        </TC.Row>
      );
    }
  }

  return (<Error statusCode={404} />) as React.ReactElement;
};

const MoneyWidgetContainer = styled.div`
  margin: auto;
  padding: 1rem;
`;

const ProvidedByBenzinga = styled.div`
  text-align: center;
  margin: auto;
  color: #000;

  svg {
    height: 16px !important;
  }
`;

export const getServerSideProps: GetServerSideProps = async ({ query, res, resolvedUrl }) => {
  try {
    // Check for Money Page
    const session = getGlobalSession();
    const postResponse = await session.getManager(ContentManager).getWordpressPost(query.widget as unknown as number);
    const postData = postResponse.ok;

    if (Array.isArray(postData?.blocks) && postData.success !== false) {
      postData.blocks = await loadServerSideBlockData(session, postData.blocks);

      return {
        props: {
          aff_id: query?.aff_id ?? null,
          disablePageTracking: query?.embedded ?? false,
          embeddedWidget: query?.embedded ?? false,
          headerProps: {
            disableOptinMonster: true,
            hideBanner: true,
            hideFooter: true,
            hideNavigationBar: query?.embedded ?? false,
            hideQuoteBar: true,
            hideheader: true,
            hidefooter: true,
            hideMenuBar: true,
          },
          metaProps: { robots: 'noindex, nofollow' },
          resolvedUrl,
          widget: postData,
          widgetId: query?.widget_id ?? null,
        },
      };
    }
    res.statusCode = 404;
    return {
      props: {
        aff_id: null,
        error: 404,
        news: [],
        topNews: [],
        topic: '',
      },
    };
  } catch {
    res.statusCode = 404;
    return {
      props: {
        aff_id: null,
        error: 404,
        news: [],
        topNews: [],
        topic: '',
      },
    };
  }
};

export default Page;

import React from 'react';
import { ProvidedByBenzingaText } from '../../../pages/widgets';
import styled, { TC } from '@benzinga/themetron';
import { Button, Dropdown, Icon, SelectMenu } from '@benzinga/core-ui';
import { TickerSelect } from '@benzinga/ticker-ui';
import copy from 'copy-to-clipboard';
import { Ticker } from '@benzinga/session';

// ** Icons
import { faChevronDown } from '@fortawesome/pro-regular-svg-icons/faChevronDown';
import { faChevronUp } from '@fortawesome/pro-regular-svg-icons/faChevronUp';
import { faChartLine, faChartCandlestick } from '@fortawesome/pro-solid-svg-icons';

interface DropdownTitleValues {
  name: string;
  value: string | number;
}

interface CheckboxTitleValues {
  name: string;
  value: boolean;
}

interface DropdownOption {
  key: string | number;
  name: string | number;
}
interface WidgetConfigProps {
  ticker?: string;
  setTicker?: (ticker: string) => void;
  widgetName?: string;

  accentSelect?: boolean;
  accentColor?: string;
  setParentAccentColor?: (color: string) => void;

  tickerSelect?: boolean;
  multiTickerSelect?: boolean;
  selectedMultiTickers?: Ticker[];
  multiTickersOnChange?: (tickers: Ticker | Ticker[] | null) => void;
  multiTickersOnRemove?: (ticker: Ticker | Ticker[] | null) => void;

  chartTypeSelect?: boolean;
  chartType?: string;
  setParentChartType?: (chartType: string) => void;
  setParentBarType?: (barType: string) => void;

  autosize?: boolean;
  setParentAutosize?: (autosize: boolean) => void;

  widgetHeight?: string;
  setWidgetHeight?: (height: string) => void;
  widgetMaxHeight?: string;
  setWidgetMaxHeight?: (height: string) => void;
  widgetWidth?: string;
  setWidgetWidth?: (width: string) => void;
  widgetMaxWidth?: string;
  setWidgetMaxWidth?: (width: string) => void;
  noSettings?: boolean;
  code?: string;
  // We want to allow there to be an array of options to be passed along with a name to display a dropdown
  // This will be used for the chart type dropdown
  dropdownTitleValues?: DropdownTitleValues[];
  dropdownOptions?: DropdownOption[][];
  dropdownToggles?: ((value: boolean) => void)[];
  dropdownOpens?: boolean[];
  dropdownChanges?: ((value: string) => void)[];

  extraEmbedText?: string;

  checkboxTitleValues?: CheckboxTitleValues[];
  checkboxToggles?: ((value: boolean) => void)[];
}

const WidgetConfig = (props: WidgetConfigProps) => {
  const {
    multiTickersOnChange,
    multiTickersOnRemove,
    selectedMultiTickers,
    setParentAccentColor,
    setParentAutosize,
    setParentBarType,
    setParentChartType,
    setTicker,
    ticker,
  } = props;
  const [accentColor, setAccentColor] = React.useState(props.accentColor || '26a0fc');
  const [selectedTicker, setSelectedTicker] = React.useState<Ticker | null>(ticker ? { symbol: ticker } : null);
  const [autosize, setAutosize] = React.useState<boolean>(props.autosize || true);
  const [widgetHeight, setWidgetHeight] = React.useState(props.widgetHeight || '1080');
  const [widgetMaxHeight, setWidgetMaxHeight] = React.useState('1080');
  const [widgetWidth, setWidgetWidth] = React.useState(props.widgetWidth || '1080');
  const [widgetMaxWidth, setWidgetMaxWidth] = React.useState('1080');
  const [embedCodeType, setEmbedCodeType] = React.useState<string>('script');

  const handleTickerSelect = React.useCallback(
    option => {
      setSelectedTicker(option);
      setTicker && setTicker(option?.symbol);
    },
    [setSelectedTicker, setTicker],
  );

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value.length === 0) {
      return;
    }
    setAccentColor(e.target.value);
    setParentAccentColor && setParentAccentColor(e.target.value);
  };

  const [chartDropdownOpen, setChartDropdownOpen] = React.useState(false);
  const [barType, setBarType] = React.useState(props.chartType || '0');
  const [chart_type, setChartType] = React.useState(getChartType(props.chartType || '0'));

  const embedCode = `<script src="https://www.benzinga.com/widgets.js"></script>\n<div class="bz-widget" data-name="${
    props.widgetName
  }"${props.tickerSelect ? ` data-ticker="${selectedTicker?.symbol || ticker}"` : ''}${
    props.chartTypeSelect ? ` data-bars="${barType}"` : ''
  }${props.accentSelect ? ` data-color="${accentColor}"` : ''}${
    !autosize ? ` data-width="${widgetWidth}" data-maxWidth="${widgetMaxWidth}" data-height="${widgetHeight}"` : ''
  }${props.extraEmbedText ?? ''}></div>\n${ProvidedByBenzingaText}`;

  const iframeEmbedCode = `<iframe src="https://widgets.benzinga.com/w/${props.widgetName}"${
    props.tickerSelect ? ` data-ticker=${selectedTicker?.symbol || ticker}` : ''
  }${props.chartTypeSelect ? ` data-bars=${barType}` : ''}${props.accentSelect ? ` data-color=${accentColor}` : ''} width="100%" frameborder="0"></iframe>`;

  const copyCode = () => {
    const textToCopy = props.code ?? embedCode;
    copy(textToCopy?.replace(/\n/g, '')?.replace(/\s\s+/g, ' '));
  };

  const handleChartTypeChange = (e: string) => {
    setBarType(e);
    setChartType(getChartType(e));
    setParentChartType && setParentChartType(getChartType(e));
    setParentBarType && setParentBarType(e);
  };

  const handleChartDropdownToggle = React.useCallback(
    (toggle: boolean) => {
      setChartDropdownOpen(toggle);
    },
    [setChartDropdownOpen],
  );

  const ChartTypePicker = () => {
    return (
      <Dropdown
        distance={0}
        onClose={undefined}
        onToggle={handleChartDropdownToggle}
        open={chartDropdownOpen}
        target={
          <Button className="chart-type-picker" size="sm" variant={'flat'}>
            <Icon icon={getChartTypeIcon(chart_type)} />
            <span className="mr-1">{chart_type.charAt(0).toUpperCase() + chart_type.slice(1)}</span>
            <Icon className="chevron-down" icon={faChevronDown} />
          </Button>
        }
        trigger={'hover'}
      >
        <SelectMenu onChange={handleChartTypeChange} open={true} options={bar_types} selected={chart_type} />
      </Dropdown>
    );
  };

  return (
    <SettingsWrapper>
      <div
        className="grid grid-cols-12 grid-flow-row grid-rows-1 gap-4"
        style={{ margin: 'auto', maxWidth: '1080px', width: '100%' }}
      >
        <div className={`lg:col-span-${props.noSettings ? 12 : 6} md:col-span-12 col-span-12`}>
          <TC.Column>
            <div className="section-title">Embed Code</div>
            <div className="embed-code-options mb-2">
              <div className="btn-group gender-input">
                <label className="btn-type-radio pr-3">
                  <input
                    className="btn-radio"
                    name={'embed-type'}
                    onChange={() => {
                      setEmbedCodeType('script');
                    }}
                    required
                    type="radio"
                    value="script"
                    checked={embedCodeType === 'script'}
                  />{' '}
                  JavaScript
                </label>
                <label className="btn-type-radio">
                  <input
                    className="btn-radio"
                    name={'embed-type'}
                    onChange={() => {
                      setEmbedCodeType('iframe');
                    }}
                    type="radio"
                    value="iframe"
                    checked={embedCodeType === 'iframe'}
                  />{' '}
                  iframe
                </label>
              </div>
            </div>
            <>
              {embedCodeType === 'iframe' ? (
                <div className="embed-code-wrapper">
                  <div className="embed-code-text">{iframeEmbedCode}</div>
                </div>
              ) : (
                <>
                  <div className="embed-code-wrapper">
                    <div className="embed-code-text">
                      {props.code ? `${props.code}${ProvidedByBenzingaText}` : embedCode}
                    </div>
                  </div>
                  <Button className="copy-code-button" onClick={copyCode} variant="flat">
                    Copy Code
                  </Button>
                </>
              )}
            </>
          </TC.Column>
        </div>
        <div
          className="lg:col-span-6 md:col-span-12 col-span-12"
          style={{ display: props.noSettings ? 'none' : 'block' }}
        >
          {embedCodeType === 'script' && (
            <>
              <div className="section-title">Settings</div>
              <div className="settings-grid">
                {props.multiTickerSelect && (
                  <div className="settings-section">
                    <div className="settings-title">Symbols:</div>
                    <TickerSelect
                      multiple
                      onChange={multiTickersOnChange}
                      onRemove={multiTickersOnRemove}
                      selected={selectedMultiTickers}
                    />
                  </div>
                )}
                {props.tickerSelect && (
                  <div className="settings-section">
                    <div className="settings-title">Default Symbol:</div>
                    <TickerSelect onChange={handleTickerSelect} selected={selectedTicker} />
                  </div>
                )}
                {props.chartTypeSelect && (
                  <div className="settings-section">
                    <div className="settings-title">Chart Type:</div>
                    <ChartTypePicker />
                  </div>
                )}
                {props.accentSelect && (
                  <div className="settings-section">
                    <div className="settings-title">Accent Color:</div>
                    <div className="accent-color-sample" style={{ background: '#' + accentColor }} />
                    <input
                      className="accent-color-input"
                      onChange={handleColorChange}
                      type="text"
                      value={accentColor}
                    />
                  </div>
                )}
                <div className="settings-section">
                  <div className="settings-title">Autosize:</div>
                  <input
                    checked={autosize}
                    className="autosize-checkbox"
                    onChange={event => {
                      setAutosize(event.target.checked as boolean);
                      setParentAutosize && setParentAutosize(event.target.checked as boolean);
                    }}
                    type="checkbox"
                  />
                </div>
                {!autosize && (
                  <>
                    <div className="settings-section">
                      <div className="settings-title">Width:</div>
                      <input
                        className="accent-color-input"
                        max={1080}
                        min={0}
                        onChange={event => setWidgetWidth(event.target.value)}
                        type="number"
                        value={widgetWidth}
                      />
                    </div>
                    <div className="settings-section">
                      <div className="settings-title">Max Width:</div>
                      <input
                        className="accent-color-input"
                        max={1080}
                        min={0}
                        onChange={event => setWidgetMaxWidth(event.target.value)}
                        type="number"
                        value={widgetMaxWidth}
                      />
                    </div>
                    <div className="settings-section">
                      <div className="settings-title">Height:</div>
                      <input
                        className="accent-color-input"
                        max={1080}
                        min={300}
                        onChange={event => setWidgetHeight(event.target.value)}
                        type="number"
                        value={widgetHeight}
                      />
                    </div>
                    <div className="settings-section">
                      <div className="settings-title">Max Height:</div>
                      <input
                        className="accent-color-input"
                        max={1080}
                        min={300}
                        onChange={event => setWidgetMaxHeight(event.target.value)}
                        type="number"
                        value={widgetMaxHeight}
                      />
                    </div>
                  </>
                )}
                {/* We need to show a dropdown for each dropdownOptions + name, use the dropdownValues + setDropdownValues values at the same index too */}
                {/* We loop through the dropdownOptions (which holds the label name + current value), Toggles, Opens,   */}
                {Array.isArray(props.dropdownTitleValues) &&
                  props.dropdownTitleValues.map((dropdownOptionSection: DropdownTitleValues, index) => (
                    <div className="settings-section" key={index}>
                      <div className="settings-title">{dropdownOptionSection.name}:</div>
                      <Dropdown
                        distance={0}
                        onClose={undefined}
                        onToggle={props.dropdownToggles?.[index]}
                        open={props.dropdownOpens?.[index]}
                        target={
                          <Button className="chart-type-picker" size="sm" variant={'flat'}>
                            <span className="mr-1">
                              {titleCase(
                                props.dropdownOptions?.[index]?.find(
                                  option => option.key === dropdownOptionSection.value,
                                )?.name,
                              )}
                            </span>
                            <Icon
                              className="chevron-down"
                              icon={props.dropdownOpens?.[index] ? faChevronUp : faChevronDown}
                            />
                          </Button>
                        }
                        trigger={'click'}
                      >
                        <SelectMenu
                          onChange={props.dropdownChanges?.[index]}
                          open={true}
                          options={props.dropdownOptions?.[index]}
                          selected={dropdownOptionSection.value}
                        />
                      </Dropdown>
                    </div>
                  ))}
                {Array.isArray(props.checkboxTitleValues) &&
                  props.checkboxTitleValues.map((checkboxOptionSection: CheckboxTitleValues, index) => (
                    <div className="settings-section" key={index}>
                      <div className="settings-title">{checkboxOptionSection.name}:</div>
                      <input
                        checked={checkboxOptionSection.value}
                        className="autosize-checkbox"
                        onChange={event => {
                          props.checkboxToggles && props.checkboxToggles[index](event.target.checked as boolean);
                        }}
                        type="checkbox"
                      />
                    </div>
                  ))}
              </div>
            </>
          )}
        </div>
      </div>
    </SettingsWrapper>
  );
};

const bar_types = [
  { key: '0', name: 'line' },
  { key: '1', name: 'candlestick' },
];

const getChartType = (value: string | number) => {
  if (value === '1') {
    return 'candlestick';
  } else {
    return 'line';
  }
};

const getChartTypeIcon = value => {
  if (value === '1') {
    return faChartCandlestick;
  } else {
    return faChartLine;
  }
};

// Write a function that returns a titlecase of a string
const titleCase = (str: string | number | undefined) => {
  if (typeof str !== 'string') {
    return str;
  }
  return str
    .toLowerCase()
    .split(' ')
    .map(function (word) {
      return word?.replace(word[0], word[0].toUpperCase());
    })
    .join(' ');
};

export default WidgetConfig;

export const SettingsWrapper = styled.div`
  justify-content: center;
  margin-bottom: 3rem;
  margin-left: auto;
  margin-top: auto;
  margin-right: auto;
  max-width: 1280px;
  width: 100%;
  vertical-align: middle;
  .embed-code-section {
    display: flex;
    flex-direction: column;
    margin-bottom: 1rem;
  }
  .select-menu-list-item {
    text-transform: capitalize;
  }
  .chart-type-picker {
    border: none !important;
    box-shadow: none !important;
    display: flex;
    font-size: 18px !important;
    gap: 8px;
    margin-top: 0.25rem;
  }
  .copy-code-button {
    background: rgba(26, 121, 255, 0.025);
    border: 1px solid rgba(26, 121, 255, 0.5);
    color: #1a79ff !important;
    margin-left: auto;
    margin-right: auto;
    margin-top: 5px;
    top: 0;
    display: flex;
  }
  .section-title {
    font-style: normal;
    font-weight: 700;
    font-size: 24px;
    line-height: 32px;
    margin-bottom: 16px;
    margin-left: 25px;
    margin-top: 24px;
  }
  .settings-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }
  .settings-section {
    background: rgba(206, 221, 242, 0.15);
    border: 0.5px solid #e1ebfa;
    border-radius: 4px;
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 16px;
    gap: 10px;
  }
  .settings-title {
    font-style: normal;
    font-weight: 400;
    font-size: 16px;
    line-height: 20px;
    color: #99aecc;
  }

  .accent-color-sample {
    width: 20px;
    height: 20px;
    border-radius: 4px;
  }
  .accent-color-input {
    background: transparent;
    border: none;
    color: #192940;
    font-size: 18px;
    line-height: 24px;
    font-weight: 700;
    font-style: normal;
    padding: 0;
  }

  .embed-code-options {
    margin-left: 15px;
    margin-right: 15px;
  }

  .embed-code-wrapper {
    border: 1px solid #f0f3fa;
    border-radius: 8px;
    box-sizing: border-box;
    margin-left: 15px;
    margin-right: 15px;
  }

  .embed-code-text {
    background: #0000;
    border: 0;
    color: #6a6d78;
    direction: ltr;
    font-family:
      Menlo,
      Ubuntu Mono,
      Consolas,
      source-code-pro,
      monospace;
    font-size: 14px;
    height: 100%;
    line-height: 20px;
    min-height: 156px;
    resize: none;
    -webkit-user-select: text;
    user-select: text;
    white-space: pre-wrap;
    width: 100%;
    word-break: break-word;
  }

  .autosize-checkbox {
    height: 20px;
    width: 20px;
    border-radius: 4px;
  }
`;

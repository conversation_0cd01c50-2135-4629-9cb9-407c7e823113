import { SafePromise } from '@benzinga/safe-await';
import { Subscribable, Subscription } from '@benzinga/subscribable';
import { Session } from '@benzinga/session';
import { shuffleArray } from '@benzinga/utils';
import {
  ArticleData,
  AuthorId,
  Author,
  Campaigns,
  NodeId,
  InternalNode,
  DraftArticle,
  EditorialArticlePreviewResponse,
  RelevantArticles,
  RelevantArticlesIds,
  TrendingIndiaTopics,
  WNSTNFollowUpQuestionsResponse,
} from './entities/article';
import { ArticleRequest, ArticleRequestEvent } from './request';
import { ArticleStore, ArticleStoreEvent } from './store';
import { CommentCountResponse } from './entities';

export type ArticleManagerEvent = ArticleRequestEvent | ArticleStoreEvent;

export class ArticleManager extends Subscribable<ArticleManagerEvent> {
  private store: ArticleStore;
  private request: ArticleRequest;
  private storeSubscription?: Subscription<ArticleStore>;
  private requestSubscription?: Subscription<ArticleRequest>;

  constructor(session: Session) {
    super();
    this.request = new ArticleRequest(session);
    this.store = new ArticleStore();
  }

  public static getName = () => 'benzinga-article';

  public getStoredAuthorByPath = (authorSlug: string): Author | undefined => {
    return this.store.getAuthors()?.find(a => a.name === authorSlug);
  };

  public getStoredAuthorById = (authorId: AuthorId): Author | undefined => {
    return this.store.getAuthors()?.find(a => a.uid === authorId);
  };

  public getStoredArticleById = (nodeId: NodeId): InternalNode | undefined => {
    return this.store.getArticles()?.find(a => a.NodeID === nodeId);
  };

  public getStoredAuthors = () => {
    return this.store.getAuthors();
  };

  public getCampaigns = async (nodeId: NodeId): SafePromise<Campaigns> => {
    return this.request.getCampaigns(nodeId);
  };

  public getAuthorByPath = async (authorSlug: string, force?: boolean): SafePromise<Author | undefined> => {
    if (force || this.store.shouldFetchAuthorByPath(authorSlug) === true) {
      const resp = await this.request.getAuthorByPath(authorSlug);
      if (resp.err) {
        return { ok: undefined };
      } else {
        this.store.addAuthor(resp.ok);

        return resp;
      }
    } else {
      return { ok: this.getStoredAuthorByPath(authorSlug) };
    }
  };

  public getAuthor = async (authorID: AuthorId, force?: boolean): SafePromise<Author | undefined> => {
    if (force || this.store.shouldFetchAuthor(authorID) === true) {
      const resp = await this.request.getAuthor(authorID);
      if (resp.err) {
        return { ok: undefined };
      } else {
        this.store.addAuthor(resp.ok);

        return resp;
      }
    } else {
      return { ok: this.getStoredAuthorById(authorID) };
    }
  };

  public getInternalNode = async (nodeID: NodeId): SafePromise<InternalNode | undefined> => {
    const resp = await this.request.getInternalNode(nodeID);
    if (resp.err) {
      return { err: resp.err };
    } else {
      this.store.addArticle(resp.ok);
      return resp;
    }
  };

  public getArticle = async (nodeID: NodeId): SafePromise<ArticleData | undefined> => {
    return this.request.getArticle(nodeID);
  };

  public getDraftArticle = async (nodeId: NodeId, token?: string): SafePromise<DraftArticle | undefined> => {
    return this.request.getDraftArticle(nodeId, token);
  };

  public getEditorialArticlePreview = async (nodeId: number | string): SafePromise<EditorialArticlePreviewResponse> => {
    return await this.request.getEditorialArticlePreview(nodeId);
  };

  public getArticlesWithIds = async (ids: number[]): SafePromise<ArticleData[]> => {
    return await this.request.getArticlesWithIds(ids);
  };

  public getTrendingIndiaStories = async (): SafePromise<ArticleData[]> => {
    const response = await this.request.getTrendingIndiaStoriesIds();
    if (Array.isArray(response.ok)) {
      const nodeIds = response.ok.map(item => Number(item.nid));
      return await this.getArticlesWithIds(nodeIds);
    } else if (response.err) {
      return { err: response.err };
    } else {
      return { ok: [] };
    }
  };

  public getTrendingIndiaTopics = async (): SafePromise<TrendingIndiaTopics[]> => {
    return await this.request.getTrendingIndiaTopics();
  };

  public getRelevantArticlesIDs = async (article: ArticleData): SafePromise<RelevantArticlesIds> => {
    const relevantArticlesRes = await this.request.getRelevantArticles();
    const { err, ok: relevantArticles } = relevantArticlesRes;

    if (err) {
      return { err };
    }

    if (relevantArticles && Array.isArray(relevantArticles?.sponsored)) {
      shuffleArray(relevantArticles.sponsored);
    }

    if (relevantArticles && Array.isArray(relevantArticles?.topStories)) {
      shuffleArray(relevantArticles.topStories);
    }

    let sponsored: number[] = [];
    let topStories: number[] = [];

    if (relevantArticles?.sponsored?.length) {
      sponsored = relevantArticles.sponsored.splice(0, 5);
    }

    if (relevantArticles?.topStories?.length) {
      const relevantArticlesIds = relevantArticles.topStories.splice(0, 15);

      if (Array.isArray(relevantArticlesIds) && article) {
        topStories = relevantArticlesIds.filter(topStoryId => {
          return topStoryId !== article.nodeId;
        });
      }
    }

    return {
      ok: {
        sponsored,
        topStories,
      },
    };
  };

  public getRelevantArticles = async (article: ArticleData): SafePromise<RelevantArticles> => {
    const relevantArticlesRes = await this.request.getRelevantArticles();
    const { err, ok: relevantArticles } = relevantArticlesRes;

    if (err) {
      return { err };
    }

    if (relevantArticles && Array.isArray(relevantArticles?.sponsored)) {
      shuffleArray(relevantArticles.sponsored);
    }

    if (relevantArticles && Array.isArray(relevantArticles?.topStories)) {
      shuffleArray(relevantArticles.topStories);
    }

    let sponsored: ArticleData[] | undefined = [];
    let topStories: ArticleData[] | undefined = [];

    if (relevantArticles?.sponsored?.length) {
      const sponsoredReq = await this.getArticlesWithIds(relevantArticles.sponsored.splice(0, 5));
      sponsored = sponsoredReq.ok;
    }

    if (relevantArticles?.topStories?.length) {
      const relevantArticlesIds = relevantArticles.topStories.splice(0, 15);
      const topStoriesReq = await this.getArticlesWithIds(relevantArticlesIds);
      topStories = topStoriesReq.ok;
      if (Array.isArray(topStories) && article) {
        topStories = topStories.filter(topStory => {
          return topStory.nodeId !== article.nodeId;
        });
      }
      topStories = topStories?.splice(0, 15);
      // topStories?.forEach(async story => {
      //   const commentCountRes = await this.getCommentCount(story.nodeId as number);
      //   story.commentCount = commentCountRes.ok?.data?.count ?? 0;
      // });
    }

    return {
      ok: {
        sponsored,
        topStories,
      },
    };
  };

  getCommentCount = async (id: number): SafePromise<CommentCountResponse | undefined> => {
    return this.request.getCommentCount(id);
  };

  getFollowUpQuestions = async (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    return this.request.getFollowUpQuestions(nodeId);
  };

  getFollowUpQuestionsInternal = async (nodeId: number | string): SafePromise<WNSTNFollowUpQuestionsResponse> => {
    return this.request.getFollowUpQuestionsInternal(nodeId);
  };

  protected onFirstSubscription(): void {
    this.requestSubscription = this.request.listen(event => this.dispatch(event));
    this.storeSubscription = this.store.listen(event => this.dispatch(event));
  }

  protected onZeroSubscriptions(): void {
    this.requestSubscription?.unsubscribe();
    this.storeSubscription?.unsubscribe();
  }
}

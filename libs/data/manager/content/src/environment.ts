export class ContentEnvironment {
  public static getName = () => 'benzinga-content';
  public static getEnvironment = (env: Record<string, string>) => ({
    internalMoneyLayoutUrl: new URL(env.internalMoneyLayoutUrl ?? 'https://www.benzinga.com/lavapress/api/'),
    internalMoneyUrl: new URL(env.internalMoneyUrl ?? 'https://www.benzinga.com/lavapress/api/'),
    moneyJobsUrl: new URL(env.moneyJobsUrl ?? 'https://money-jobs.benzinga.com/api/'),
    // moneyUrl: new URL(env.moneyUrl ?? 'https://www.benzinga.com/money/api/'),
    servicesKey: env.servicesKey ?? '2RiuR92vjytxS8r93w3c8WTpGSd3y9Gk',
    toolsUrl: new URL(env.url ?? 'https://tools.zing.benzinga.com/api/'),
    url: new URL(env.url ?? 'https://www.benzinga.com/lavapress/api/'),
    widgetUrl: new URL(env.widgetUrl ?? 'https://widgets.benzinga.com/api/'),
  });
}

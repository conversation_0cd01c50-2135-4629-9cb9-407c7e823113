import { SafeError, SafePromise } from '@benzinga/safe-await';
import { ExtendedSubscribable } from '@benzinga/subscribable';

import {
  Entity,
  PostsQuery,
  Profile,
  RedirectResponse,
  TranslationResponse,
  ReviewForm,
  WordpressPage,
  WordpressPost,
  Podcast,
  PodcastsQuery,
  ProductResponse,
  TermResponse,
  ZipCodeQuery,
  ZipCodeResponse,
  MortgageData,
  MortgageDataResponse,
  CampaignResponse,
  MenuInterface,
  Show,
  ShowsQuery,
  VertialQuery,
  RequestDisclosureByParams,
  PartnerDisclosure,
  WordpressLayout,
  GetImpressionsParams,
  CreateImpressionsParams,
  Impression,
  GetImpressionResponse,
  RegisterImpressionResponse,
  ImpressionBase,
  PodcastShows,
  PodcastPostQuery,
  PodcastPost,
  WebStory,
  DentalInsuranceData,
  DentalInsuranceResponse,
  AskTheExpert,
  AskTheExpertResponse,
  SponsoredContentArticlesParams,
  CryptoExchangeRateResponse,
  NodeLayout,
  Country,
  CountriesList,
  ProductFilterOptions,
  CompareProductFieldsResponse,
  SubscribeToNewsletterRequest,
  SubscribeToNewsletterResponse,
} from './entities';
import { ContentRestful, ContentRestfulTools, ContentRestfulImpressions } from './restful';
import { ContentRestfulMoney } from './restful';
import { Session } from '@benzinga/session';
import { StoryObject } from '@benzinga/advanced-news-manager';

interface ErrorEvent {
  error: SafeError;
  errorType:
    | 'get_node_layout_error'
    | 'get_entity_error'
    | 'get_profile_error'
    | 'get_redirect_error'
    | 'get_country_error';
  type: 'error';
}

interface GetLayoutEvent {
  layout: NodeLayout;
  type: 'get_node_layout';
}

interface GetRedirectEvent {
  type: 'get_redirect';
}

interface GetEntityEvent {
  entity: Entity;
  type: 'get_entity';
}

interface GetProfileEvent {
  profile: Profile;
  type: 'get_profile';
}

export type ContentRestfulEvent = ErrorEvent | GetLayoutEvent | GetEntityEvent | GetProfileEvent | GetRedirectEvent;

export interface ContentFunctions {
  getNodeLayout: ContentRequest['getNodeLayout'];
  getRedirectByUrl: ContentRequest['getRedirectByUrl'];
  getEntity: ContentRequest['getEntity'];
  getProfile: ContentRequest['getProfile'];
  getNodeTranslation: ContentRequest['getNodeTranslation'];
  getContentImpressions: ContentRequest['getContentImpressions'];
  createContentImpressions: ContentRequest['createContentImpressions'];
  registerContentImpression: ContentRequest['registerContentImpression'];
  updateContentLimitation: ContentRequest['updateContentLimitation'];
}

export class ContentRequest extends ExtendedSubscribable<ContentRestfulEvent, ContentFunctions> {
  private restful: ContentRestful;
  private moneyRestful: ContentRestfulMoney;
  private toolsRestful: ContentRestfulTools;
  private impressionsRestful: ContentRestfulImpressions;

  constructor(session: Session) {
    super();
    this.restful = new ContentRestful(session);
    this.moneyRestful = new ContentRestfulMoney(session);
    this.toolsRestful = new ContentRestfulTools(session);
    this.impressionsRestful = new ContentRestfulImpressions(session);
  }

  public getNavigation = async (postType: string): SafePromise<MenuInterface> => {
    return this.restful.getNavigation(postType);
  };

  public getVertical = async (params: VertialQuery): SafePromise<any> => {
    return this.restful.getVertical(params);
  };

  public getRedirectByUrl = async (url: string): SafePromise<RedirectResponse> => {
    const response = await this.toolsRestful.getRedirectByUrl(url);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_redirect_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        type: 'get_redirect',
      });
    }
    return response;
  };

  public getPoll = async (id: number): SafePromise<any> => {
    return this.moneyRestful.getPoll(id);
  };

  public postPollSubmission = async (id: number, data: any): SafePromise<any> => {
    return this.moneyRestful.postPollSubmission(id, data);
  };

  public getNodeLayout = async (id: string): SafePromise<NodeLayout> => {
    const response = await this.restful.getNodeLayout(id);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_node_layout_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        layout: response.ok,
        type: 'get_node_layout',
      });
    }
    return response;
  };

  public getEntity = async (id: string): SafePromise<Entity> => {
    const response = await this.restful.getEntity(id);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_entity_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        entity: response.ok,
        type: 'get_entity',
      });
    }
    return response;
  };

  public getProfile = async (id: string): SafePromise<Profile> => {
    const response = await this.restful.getProfile(id);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_profile_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        profile: response.ok,
        type: 'get_profile',
      });
    }
    return response;
  };

  public getProfileWithPath = async (path: string): SafePromise<Profile> => {
    return this.restful.getProfileWithPath(path);
  };

  public getPost = async (post_id: number | string): SafePromise<WordpressPost> => {
    return this.restful.getPost(post_id);
  };

  public getPage = async (page_id: number | string): SafePromise<WordpressPage> => {
    return this.restful.getPage(page_id);
  };

  public getPageWithPath = async (path: string): SafePromise<WordpressPage> => {
    return this.restful.getPageWithPath(path);
  };

  public getPageWithPathBySiteCode = async (path: string, siteCode: string): SafePromise<WordpressPage> => {
    return this.restful.getPageWithPathBySiteCode(path, siteCode);
  };

  public getPortfolioPage = async (path: string): SafePromise<WordpressPage> => {
    return this.restful.getPortfolioPage(path);
  };

  public getTermByPath = async (path: string): SafePromise<TermResponse> => {
    return await this.restful.getTermByPath(path);
  };

  public getTermByName = async (name: string): SafePromise<TermResponse> => {
    return await this.restful.getTermByName(name);
  };

  public getTermById = async (tid: number | string): SafePromise<TermResponse> => {
    return await this.restful.getTermById(tid);
  };

  public clearCache = async (path: string): SafePromise<any> => {
    return this.restful.clearCache(path);
  };

  public clearNodeCache = async (id: number): SafePromise<any> => {
    return this.restful.clearNodeCache(id);
  };

  public getTopicByName = async (name: string): SafePromise<WordpressLayout> => {
    return this.restful.getTopicByName(name);
  };

  public getWordpressPost = async (post_id: number | string): SafePromise<WordpressPost> => {
    return this.restful.getWordpressPost(post_id);
  };

  public getMoneyWidget = async (slug: string): SafePromise<WordpressPost> => {
    return this.restful.getMoneyWidget(slug);
  };

  public getPostCampaigns = async (post_id: number): SafePromise<any> => {
    return this.restful.getPostCampaigns(post_id);
  };

  public getGoLinks = async (slugs: string[]): SafePromise<any> => {
    return this.restful.getGoLinks(slugs);
  };

  public getPartnerWithPath = async (path: string): SafePromise<Profile> => {
    return this.restful.getPartnerWithPath(path);
  };

  public getProductFilters = async (path: string): SafePromise<ProductFilterOptions> => {
    return this.restful.getProductFilters(path);
  };

  public getPartnerDisclosurePageBySlug = async (slug: string): SafePromise<WordpressPage> => {
    return this.restful.getPartnerDisclosurePageBySlug(slug);
  };

  public getExchangeRate = async (): SafePromise<CryptoExchangeRateResponse> => {
    return this.restful.getExchangeRate();
  };

  public getPartnerDisclosureBySlug = async (
    slug: string,
    params: Omit<RequestDisclosureByParams, 'topics' | 'channels'>,
  ): SafePromise<PartnerDisclosure> => {
    return this.restful.getPartnerDisclosureBySlug(slug, params);
  };

  public getPartnerDisclosureByNodeParams = async (
    params: RequestDisclosureByParams,
  ): SafePromise<PartnerDisclosure> => {
    const requestParams = {
      short: params?.short || false,
      'topics[]': params?.topics,
    };

    if (params?.channels) {
      requestParams['channels[]'] = params?.channels;
    }

    return this.restful.getPartnerDisclosureByNodeParams(requestParams);
  };

  public getPosts = async (query: PostsQuery): SafePromise<WordpressPost[]> => {
    if (Array.isArray(query.posts_urls)) {
      query.posts_urls.forEach((url, index) => {
        query[`posts_urls[${index}]`] = url;
      });
      delete query.posts_urls;
    }

    return this.restful.getPosts(query);
  };

  public getCustomPosts = async (query: PostsQuery): SafePromise<WordpressPost[]> => {
    return this.restful.getCustomPosts(query);
  };

  public getIndiaWebStories = async (limit: number): SafePromise<WebStory[]> => {
    return await this.restful.getIndiaWebStories(limit);
  };

  public getNodeTranslation = async (node_id: string): SafePromise<TranslationResponse> => {
    return this.toolsRestful.getNodeTranslation(node_id);
  };

  public getPodcastEpisodes = async (query: PodcastsQuery): SafePromise<Podcast[]> => {
    const response = await this.restful.getPodcastEpisodes(query);
    return { ok: response?.ok?.data ?? [] };
  };

  public getPodcastsByShow = async (show: PodcastShows): SafePromise<Podcast[]> => {
    const response = await this.restful.getPodcastsByShow(show);
    return { ok: response?.ok?.data ?? [] };
  };

  public getPodcastPost = async (query: PodcastPostQuery): SafePromise<PodcastPost> => {
    const response = await this.restful.getPodcastPost(query);
    return response;
  };

  public getPodcastPostById = async (id: number): SafePromise<PodcastPost> => {
    const response = await this.restful.getPodcastPostById(id);
    return response;
  };

  public getShowById = async (id: number): SafePromise<Show | undefined> => {
    const response = await this.restful.getShowById(id);
    return { ok: response?.ok?.data };
  };

  public getShows = async (query: ShowsQuery): SafePromise<Show[]> => {
    const response = await this.restful.getShows(query);
    return { ok: response?.ok?.data ?? [] };
  };

  public submitReview = async (form: ReviewForm, review: any): Promise<any> => {
    return this.moneyRestful.submitReview(form, review);
  };

  public getStateAvailability = async (query: ZipCodeQuery): SafePromise<ZipCodeResponse> => {
    return this.restful.getStateAvailability(query);
  };

  public getCampaigns = async (): SafePromise<CampaignResponse> => {
    return this.toolsRestful.getCampaigns();
  };

  public saveMortageLead = async (data: MortgageData): SafePromise<MortgageDataResponse> => {
    return this.toolsRestful.saveMortageLead(data);
  };

  public saveDentalInsuranceLead = async (data: DentalInsuranceData): SafePromise<DentalInsuranceResponse> => {
    return this.toolsRestful.saveDentalInsuranceLead(data);
  };

  public saveAskTheExpertComment = async (data: AskTheExpert): SafePromise<AskTheExpertResponse> => {
    return this.toolsRestful.saveAskTheExpertComment(data);
  };

  public subscribeToNewsletter = async (
    data: SubscribeToNewsletterRequest,
  ): SafePromise<SubscribeToNewsletterResponse> => {
    return this.toolsRestful.subscribeToNewsletter(data);
  };

  public getProduct = async (query: PostsQuery): SafePromise<ProductResponse> => {
    return this.restful.getProduct(query);
  };

  public getCustomFieldsLabel = async (query: PostsQuery): SafePromise<CompareProductFieldsResponse> => {
    return this.restful.getCustomFieldsLabel(query);
  };

  public getContentImpressions = async (query: GetImpressionsParams): SafePromise<GetImpressionResponse> => {
    return this.impressionsRestful.getContentImpressions(query);
  };

  public createContentImpressions = async (query: CreateImpressionsParams): SafePromise<Impression[]> => {
    return this.impressionsRestful.createContentImpressions(query);
  };

  public registerContentImpression = async (content_id: number): SafePromise<RegisterImpressionResponse> => {
    return this.impressionsRestful.registerContentImpression(content_id);
  };

  public updateContentLimitation = async (
    content_id: string,
    params: Partial<ImpressionBase>,
  ): SafePromise<Impression> => {
    return this.impressionsRestful.updateContentLimitation(content_id, params);
  };

  public getSponsoredContentArticles = async (params?: SponsoredContentArticlesParams): SafePromise<StoryObject[]> => {
    return this.restful.getSponsoredContentArticles(params);
  };

  public onSubscribe = (): ContentFunctions => ({
    createContentImpressions: this.createContentImpressions,
    getContentImpressions: this.getContentImpressions,
    getEntity: this.getEntity,
    getNodeLayout: this.getNodeLayout,
    getNodeTranslation: this.getNodeTranslation,
    getProfile: this.getProfile,
    getRedirectByUrl: this.getRedirectByUrl,
    registerContentImpression: this.registerContentImpression,
    updateContentLimitation: this.updateContentLimitation,
  });
}

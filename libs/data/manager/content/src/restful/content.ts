import { SafePromise } from '@benzinga/safe-await';

import {
  Entity,
  CryptoExchangeRateResponse,
  Profile,
  PostsQuery,
  WordpressPage,
  WordpressPost,
  PodcastsResponse,
  PodcastsQuery,
  TermResponse,
  ProductResponse,
  ZipCodeQuery,
  ZipCodeResponse,
  MenuInterface,
  ShowsResponse,
  ShowsQuery,
  VertialQuery,
  PartnerDisclosure,
  WordpressLayout,
  PodcastShows,
  ShowResponse,
  PodcastPostQuery,
  PodcastPost,
  WebStory,
  SponsoredContentArticlesParams,
  NodeLayout,
  CompareProductFieldsResponse,
  ProductFilterOptions,
} from '../entities';
import { RestfulClient, Session } from '@benzinga/session';
import { ContentEnvironment } from '../environment';
import { StoryObject } from '@benzinga/advanced-news-manager';

export class ContentRestful extends RestfulClient {
  constructor(session: Session) {
    super(session.getEnvironment(ContentEnvironment).url, session);
  }

  getNavigation = (postType: string): SafePromise<MenuInterface> => {
    const url = this.URL(`get-navigation?post_type=${postType}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getVertical = (params: VertialQuery): SafePromise<any> => {
    const url = this.URL(`vertical`, params);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getNodeLayout = (id: string): SafePromise<NodeLayout> => {
    const hostname = this.session.getEnvironment(ContentEnvironment).internalMoneyLayoutUrl;
    const url = this.URL(`${hostname}node/${id}/layout`);
    // console.log('get node layout: ', url);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getEntity = (id: string): SafePromise<Entity> => {
    const url = this.URL(`entities/${id}`);
    // console.log(url);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getProfile = (id: string): SafePromise<Profile> => {
    const url = this.URL(`entities/${id}/profile`);
    // console.log("URL: ", url)
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getProfileWithPath = (path: string): SafePromise<Profile> => {
    const url = this.URL(`entities/profile/by-path`, { path: path });
    // console.log('getProfileWithPath URL: ', url);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPost = (post_id: number | string): SafePromise<WordpressPost> => {
    const url = this.URL(`posts/${post_id}`);
    // console.log('URL: ', url);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPage = (page_id: number | string): SafePromise<WordpressPage> => {
    const url = this.URL(`pages/${page_id}`);
    // console.log('getPage: ', url);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPageWithPath = (path: string): SafePromise<WordpressPage> => {
    const url = this.URL(`wordpress?path=${path}`);
    // console.log('getPageWithPath:');
    // console.log(url);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin', timeout: 7000 });
  };

  getPageWithPathBySiteCode = (path: string, siteCode: string): SafePromise<WordpressPage> => {
    const url = this.URL(`wordpress/${siteCode}/page?path=${path}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPortfolioPage = (path: string): SafePromise<WordpressPage> => {
    const url = this.URL(`portfolio?path=${path}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin', timeout: 7000 });
  };

  getTermByPath = (path: string): SafePromise<TermResponse> => {
    const url = this.URL(`https://www.benzinga.com/services/taxonomy/path`, {
      key: this.session.getEnvironment(ContentEnvironment).servicesKey,
      path,
    });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin', mode: 'cors' });
  };

  getTermByName = (name: string): SafePromise<TermResponse> => {
    const url = this.URL(`https://www.benzinga.com/services/taxonomy/name/${name}`, {
      key: this.session.getEnvironment(ContentEnvironment).servicesKey,
    });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin', mode: 'cors' });
  };

  getTermById = (tid: number | string): SafePromise<TermResponse> => {
    const url = this.URL(`https://www.benzinga.com/services/taxonomy/tid/${tid}`, {
      key: this.session.getEnvironment(ContentEnvironment).servicesKey,
    });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin', mode: 'cors' });
  };

  clearCache = (path: string): SafePromise<WordpressPage> => {
    const url = this.URL(`wordpress/clear-cache?path=${path}`, { t: Date.now() });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  clearNodeCache = (id: number): SafePromise<any> => {
    const url = this.URL(`node/${id}/clear-cache`, { t: Date.now() });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getTopicByName = (name: string): SafePromise<WordpressLayout> => {
    const url = this.URL(`topic/${name}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getWordpressPost = (post_id: number | string): SafePromise<WordpressPost> => {
    const url = this.URL(`wordpress/${post_id}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPostCampaigns = (post_id: number): SafePromise<any> => {
    const url = this.URL(`posts/${post_id}/campaigns`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getIndiaWebStories = (limit: number): SafePromise<WebStory[]> => {
    const url = this.URL(`web-stories?limit=${limit}`);
    return this.get(url, {
      allowsAnonymousAuth: true,
      credentials: 'same-origin',
    });
  };

  getPosts = (query: PostsQuery): SafePromise<WordpressPost[]> => {
    const url = this.URL(`posts`, query);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getCustomPosts = (query: PostsQuery): SafePromise<WordpressPost[]> => {
    const url = this.URL(`posts/all`, query);
    // console.log('getCustomPosts: ', url);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPartnerWithPath = (path: string): SafePromise<Profile> => {
    const url = this.URL(`partner?path=${path}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPartnerDisclosureBySlug = (slug: string, params: Record<string, unknown>): SafePromise<PartnerDisclosure> => {
    const url = this.URL(`partner-disclosure/${slug}`, params);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPartnerDisclosurePageBySlug = (slug: string): SafePromise<WordpressPage> => {
    const url = this.URL(`partner-disclosure/${slug}/page`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPartnerDisclosureByNodeParams = (params: Record<string, unknown>): SafePromise<PartnerDisclosure> => {
    const url = this.URL(`partner-disclosure`, params);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getGoLinks = (slugs: string[]): SafePromise<PartnerDisclosure> => {
    const url = this.URL(`go-link`, {
      slugs: slugs.join(','),
    });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPodcastEpisodes = (query: PodcastsQuery): SafePromise<PodcastsResponse> => {
    const url = this.URL(`podcasts`, query);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPodcastsByShow = (show: PodcastShows): SafePromise<PodcastsResponse> => {
    const url = this.URL(`podcasts/${show}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPodcastPost = (query: PodcastPostQuery): SafePromise<PodcastPost> => {
    const url = this.URL(`podcast-post`, query);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPodcastPostById = (id: number): SafePromise<PodcastPost> => {
    const url = this.URL(`podcast-post/${id}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getShowById = async (id: number): SafePromise<ShowResponse> => {
    const url = this.URL(`shows/${id}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getShows = async (query: ShowsQuery): SafePromise<ShowsResponse> => {
    const url = this.URL(`shows`, query);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getStateAvailability = (query: ZipCodeQuery): SafePromise<ZipCodeResponse> => {
    const url = this.URL(`mortgage-lead/state-available`, query);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getProduct = (query: PostsQuery): SafePromise<ProductResponse> => {
    const url = this.URL('product', query);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getCustomFieldsLabel = (query: PostsQuery): SafePromise<CompareProductFieldsResponse> => {
    const url = this.URL('product-custom-fields', query);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getProductFilters = (filters): SafePromise<ProductFilterOptions> => {
    const url = this.URL(`product-filters?filters=${filters}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getSponsoredContentArticles = (params?: SponsoredContentArticlesParams): SafePromise<StoryObject[]> => {
    const url = this.URL('sponsored-content/articles', params);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getExchangeRate = (): SafePromise<CryptoExchangeRateResponse> => {
    const url = this.URL(`crypto/exchange-rates`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getMoneyWidget = (slug: string): SafePromise<WordpressPost> => {
    const hostname = this.session.getEnvironment(ContentEnvironment).widgetUrl;
    const url = this.URL(`${hostname}widget?path=${slug}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin', timeout: 3000 });
  };
}

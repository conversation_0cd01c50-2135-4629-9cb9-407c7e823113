export interface InsidersTradeFiling {
  _id: string;
  derivative: Derivative;
  non_derivative: NonDerivative;
  signature: Signature;
  traded_share_price: number;
  traded_shares: number;
  traded_value: number;
  traded_percentage: number;
  company_cik: string;
  company_name: string;
  sector_code: string;
  sector_name: string;
  subsector_code: string;
  subsector_name: string;
  industry_group_code: string;
  industry_group_name: string;
  industry_code: string;
  industry_name: string;
  company_ticker?: string;
  symbols: any[];
  form_type: number;
  insider_names: string;
  insider_titles: string;
  previous_shares: number;
  remaining_shares: number;
  trade_dates: Date[];
  trade_types: string[];
  trade_type_string: string;
  sec_act: string;
  sec_file_number: string;
  accession_number: string;
  film_number: string;
  irs_number: string;
  state_of_incorporation: string;
  index_url: string;
  xml_url: string;
  html_url: string;
  txt_url: string;
  change_status: string;
  insider_titles_unique: string;
  trade_status: TradeStatus;
  traded_percentage_string: string;
  has_trades: boolean;
  error_detected: boolean;
  last_filing_date: string;
  footnotes: Footnote[];
  insiders: Insider[];
  first_trade_date: string;
  last_trade_date: string;
  created_at: string;
  updated_at: string;
  __v: number;
  url?: string;
}

type TradeStatus = 'BUY' | 'GRANT' | 'OPTIONS' | 'SELL' | 'SELL-OPTIONS';

export interface Derivative {
  remaining_shares: number;
  entries: Entry[];
}

export interface Entry {
  footnotes: string[];
  code: string;
  equity_swapped: string;
  ownership_nature: string;
  post_transaction_quantity: number;
  traded_share_price: number;
  traded_shares: number;
  traded_value: number;
  traded_percentage: number;
  conversion_exercise_price: number;
  title: string;
  transaction_acquired_disposed_code: string;
  error_detected: boolean;
  _id: string;
  date: string;
  created_at?: string;
  updated_at?: string;
}

export interface Footnote {
  id: string;
  note: string;
  _id: string;
}

export interface Insider {
  cik: string;
  is_director: boolean;
  is_officer: boolean;
  is_ten_percent_owner: boolean;
  name: string;
  clean_type: string;
  raw_signature: string;
  raw_name: string;
  titles: string[];
  _id: string;
}

export interface NonDerivative {
  holdings: Derivative;
  trades: Trades;
}

export interface Trades {
  acquired: Acquired;
  disposed: Acquired;
  traded_share_price: number;
  traded_shares: number;
  traded_value: number;
  traded_percentage: number;
  remaining_shares: number;
}

export interface Acquired {
  traded_share_price: number;
  traded_shares: number;
  traded_value: number;
  traded_percentage: number;
  entries: Entry[];
}

export interface Signature {
  name: string;
  date: string;
  raw: string;
}

export interface InsidersFilingResult {
  filings: {
    title: string;
    description: string;
    total_pages: number;
    total_docs: number;
    filings: InsidersFiling[];
    filters: InsiderFilingFilter[];
    presets: InsidersFilingResult;
    group_type: string;
  };
  success?: boolean;
  title?: string;
  id?: string;
}

interface InsiderFilingFilter {
  name: string;
  query: string;
  value: string;
}

export interface InsiderTradePreset {
  title: string;
  watchlistId: string;
  total_pages: number;
  total_docs: number;
  filings: InsidersFiling[];
  filters: string[];
  group_type: string;
  id: string;
}

export interface InsiderTradePresetV2 {
  id: string;
  title?: string;
  url?: string;
  description?: string;
  query_type?: string;
  group_type?: string;
  filings?: InsidersFiling[];
  filters?: {
    trade_codes?: string;
    trade_types?: string;
    last_filing_date?: string;
    company_ticker?: string;
  };
}

export interface InsidersRequestOptions {
  company_ticker?: string;
  preset_name?: string;
  page?: number;
  query?: string;
  last_filing_date?: string;
  limit?: number;
}

export interface InsidersMultipleRequestOptions {
  limit: number;
  preset1: string;
  preset2?: string;
  preset3?: string;
  preset4?: string;
  preset5?: string;
}

export interface PresetInsidersFilingResult {
  filings: InsidersFilingResult[];
  success: boolean;
}
export interface PresetInsidersResultV2 {
  presets: InsiderTradePresetV2[];
  success: boolean;
}

export interface DefaultPresetInsiders {
  id: string;
  title: string;
  description: string;
  filings: InsidersFiling[];
  type: string;
  url: string;
}
export interface DefaultInsidersFilingResult {
  title?: string;
  data: DefaultPresetInsiders[];
  success: boolean;
  id?: string;
  description?: string;
  url?: string;
}

export interface InsidersV2 {
  _id: string;
  name: string;
  cik: string;
  tickers: string[];
  exchanges: string[];
  formerNames: {
    name: string;
    from: Date;
    to: Date;
  }[];
}

export interface InsiderTradeSearchResult {
  insiders?: InsidersV2[];
  success: boolean;
  id?: string;
  title?: string;
}

export interface InsiderTradesNetWorthResult {
  insider: {
    alias: string;
    cik: string;
    companies: any[];
    filings: InsidersTradeFiling[];
    last_prices: any;
    line_chart: any;
    name: string;
    net_worth: number;
    net_worth_string: string;
    stats: any;
    trades_overall_string: string;
    trades_overview_string: string;
    updated_at: string;
  };
  success: boolean;
}

export type InsiderResultV2 = InsiderTradeSearchResult | DefaultInsidersFilingResult | InsiderTradePresetV2;
export type InsidersResult = InsidersFilingResult;
export type InsidersFiling = InsidersTradeFiling;
export type InsiderOptions = InsidersRequestOptions | InsidersMultipleRequestOptions;

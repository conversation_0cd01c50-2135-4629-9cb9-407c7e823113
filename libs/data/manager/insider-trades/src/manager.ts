import { SafePromise } from '@benzinga/safe-await';
import { ExtendedSubscribable } from '@benzinga/subscribable';

import {
  InsidersResult,
  InsiderOptions,
  InsiderTradePreset,
  DefaultPresetInsiders,
  PresetInsidersResultV2,
  InsiderTradePresetV2,
  InsiderTradeSearchResult,
  InsiderTradesNetWorthResult,
} from './entities';
import { InsiderTradesRequest, InsiderTradesRestfulEvent } from './request';
import { Session } from '@benzinga/session';

export type InsiderTradesManagerEvent = InsiderTradesRestfulEvent;

interface InsiderTradesFunctions {
  getInsiderTrades: InsiderTradesManager['getInsiderTrades'];
}

export class InsiderTradesManager extends ExtendedSubscribable<InsiderTradesManagerEvent, InsiderTradesFunctions> {
  private request: InsiderTradesRequest;

  constructor(session: Session) {
    super();
    this.request = new InsiderTradesRequest(session);
  }

  public static getName = () => 'benzinga-insider-trades';

  public getInsiderTradesPresets = async (): SafePromise<PresetInsidersResultV2> => {
    const response = await this.request.getPresetsInsiderTrade();
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getInsiderTrades = async (options: InsiderOptions): SafePromise<InsidersResult> => {
    const response = await this.request.getInsiderTrades(options);
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getInsiderTradesV2 = async (options: InsiderOptions): SafePromise<InsiderTradePresetV2> => {
    const response = await this.request.getInsiderTradesV2(options);
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getDefaultInsiderTrade = async (): SafePromise<DefaultPresetInsiders[]> => {
    const response = await this.request.getDefaultInsiderTrade();
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getPresetInsiderTrade = async (options: InsiderOptions): SafePromise<InsiderTradePresetV2> => {
    const response = await this.request.getPresetFillingsV2(options);
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getProPresetInsiderTrade = async (options: InsiderOptions): SafePromise<InsiderTradePreset> => {
    const response = await this.request.getProPresetInsiderTrade({
      ...options,
      preset2: 'insider_purchases',
      preset3: 'penny_stock_buys',
      preset4: 'insider_sales_above_100k',
      preset5: 'inside',
    });
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getInsiderTradesTextSearch = async (search): SafePromise<InsiderTradeSearchResult> => {
    const response = await this.request.getInsiderTradesTextSearch({ query: search });
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  public getInsiderTradesNetWorth = async (cik): SafePromise<InsiderTradesNetWorthResult> => {
    const response = await this.request.getInsiderTradesNetWorth(cik);
    if (response.ok) {
      return { ok: response?.ok };
    } else {
      return { err: response.err };
    }
  };

  protected onSubscribe(): InsiderTradesFunctions {
    return {
      getInsiderTrades: this.getInsiderTrades,
    };
  }
}

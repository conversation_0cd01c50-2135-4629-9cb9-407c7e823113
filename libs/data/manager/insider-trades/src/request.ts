import { SafeError, SafePromise } from '@benzinga/safe-await';
import { ExtendedSubscribable } from '@benzinga/subscribable';

import {
  InsidersResult,
  InsiderOptions,
  InsiderTradePreset,
  DefaultInsidersFilingResult,
  DefaultPresetInsiders,
  PresetInsidersResultV2,
  InsiderTradePresetV2,
  InsiderTradeSearchResult,
  InsiderResultV2,
  InsiderTradesNetWorthResult,
} from './entities';
import { InsiderTradesRestful } from './restful';
import { Session } from '@benzinga/session';

interface ErrorEvent {
  error: SafeError;
  errorType:
    | 'get_insider_trades_error'
    | 'get_preset_insider_trade_error'
    | 'get_default_insider_trade_error'
    | 'get_insider_trades_text_search_error'
    | 'get_insider_trades_net_worth_error';
  type: 'error';
}

interface InsiderTradesEvent {
  insiderTrades: InsidersResult | InsiderResultV2;
  type: 'get_filings';
}

interface InsiderTradesPresetEvent {
  presetInsiderTrades:
    | InsiderTradePreset
    | InsiderTradePreset[]
    | DefaultInsidersFilingResult
    | PresetInsidersResultV2
    | InsiderTradePresetV2
    | InsiderTradeSearchResult;
  type:
    | 'get_preset_insider_trade'
    | 'get_default_insider_trade'
    | 'get_insider_trades_text_search'
    | 'get_insider_trades_net_worth';
}

export type InsiderTradesRestfulEvent = ErrorEvent | InsiderTradesEvent | InsiderTradesPresetEvent;

interface InsiderTradesFunctions {
  getInsiderTrades: InsiderTradesRequest['getInsiderTrades'];
}

export class InsiderTradesRequest extends ExtendedSubscribable<InsiderTradesRestfulEvent, InsiderTradesFunctions> {
  private restful: InsiderTradesRestful;

  constructor(session: Session) {
    super();
    this.restful = new InsiderTradesRestful(session);
  }

  public getInsiderTrades = async (options: InsiderOptions): SafePromise<InsidersResult> => {
    const response = await this.restful.getFilings(options);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_insider_trades_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        insiderTrades: response.ok,
        type: 'get_filings',
      });
    }
    return response;
  };

  public getInsiderTradesV2 = async (options: InsiderOptions): SafePromise<InsiderTradePresetV2> => {
    const response = await this.restful.getFilingsV2(options);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_insider_trades_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        insiderTrades: response.ok,
        type: 'get_filings',
      });
    }
    return response;
  };

  public getDefaultInsiderTrade = async (): SafePromise<DefaultPresetInsiders[]> => {
    const response = await this.restful.getDefaultFilings();
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_default_insider_trade_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        presetInsiderTrades: response.ok,
        type: 'get_default_insider_trade',
      });
      return { ok: response.ok.data };
    }
    return { err: response.err };
  };

  public getPresetFillingsV2 = async (options: InsiderOptions): SafePromise<InsiderTradePresetV2> => {
    const response = await this.restful.getPresetFillingsV2(options);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_default_insider_trade_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        presetInsiderTrades: response.ok,
        type: 'get_default_insider_trade',
      });
      return { ok: response.ok };
    }
    return { err: response.err };
  };

  public getPresetsInsiderTrade = async (): SafePromise<PresetInsidersResultV2> => {
    const response = await this.restful.getPresetsInsiderTrade();
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_preset_insider_trade_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        presetInsiderTrades: response.ok,
        type: 'get_preset_insider_trade',
      });
      return { ok: response.ok };
    }
    return { err: response.err };
  };

  public getProPresetInsiderTrade = async (options: InsiderOptions): SafePromise<InsiderTradePreset> => {
    const response = await this.restful.getProPresetFilings(options);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_preset_insider_trade_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        presetInsiderTrades: response.ok,
        type: 'get_preset_insider_trade',
      });
      return { ok: response.ok };
    }
    return { err: response.err };
  };

  public getInsiderTradesTextSearch = async (options: InsiderOptions): SafePromise<InsiderTradeSearchResult> => {
    const response = await this.restful.getInsiderTradesTextSearch(options);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_insider_trades_text_search_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        presetInsiderTrades: response.ok,
        type: 'get_insider_trades_text_search',
      });
      return { ok: response.ok };
    }
    return { err: response.err };
  };

  public getInsiderTradesNetWorth = async (cik): SafePromise<InsiderTradesNetWorthResult> => {
    const response = await this.restful.getInsiderTradesNetWorth(cik);
    if (response.err) {
      this.dispatch({
        error: response.err,
        errorType: 'get_insider_trades_net_worth_error',
        type: 'error',
      });
    } else {
      this.dispatch({
        presetInsiderTrades: response.ok,
        type: 'get_insider_trades_net_worth',
      });
      return { ok: response.ok };
    }
    return { err: response.err };
  };

  public onSubscribe = (): InsiderTradesFunctions => ({
    getInsiderTrades: this.getInsiderTrades,
  });
}

import { SafePromise } from '@benzinga/safe-await';

import {
  InsidersResult,
  InsiderOptions,
  InsiderTradePreset,
  DefaultInsidersFilingResult,
  PresetInsidersResultV2,
  InsiderTradePresetV2,
  InsiderTradeSearchResult,
  InsiderTradesNetWorthResult,
} from './entities';
import { RestfulClient } from '@benzinga/session';
import { Session } from '@benzinga/session';
import { InsiderTradeEnvironment } from './environment';

export class InsiderTradesRestful extends RestfulClient {
  constructor(sessionManager: Session) {
    super(sessionManager.getEnvironment(InsiderTradeEnvironment).url, sessionManager);
  }

  getFilings = (options: InsiderOptions): SafePromise<InsidersResult> => {
    const url = this.URL(`/sec/insider-trades/api/insider-trades`);
    return this.post(url, options, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getProPresetFilings = (options: InsiderOptions): SafePromise<InsiderTradePreset> => {
    const url = this.URL(`/sec/insider-trades/api/pro/preset-trades`, { ...options });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getFilingsV2 = (options: InsiderOptions): SafePromise<InsiderTradePresetV2> => {
    const url = this.URL(`/sec/insider-trades/api/v2/insider-trades`, { ...options });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPresetFillingsV2 = (options: InsiderOptions): SafePromise<InsiderTradePresetV2> => {
    const url = this.URL(`/sec/insider-trades/api/v2/preset`, { ...options });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getDefaultFilings = (): SafePromise<DefaultInsidersFilingResult> => {
    const url = this.URL(`/sec/insider-trades/api/v2/default-trades`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getPresetsInsiderTrade = (): SafePromise<PresetInsidersResultV2> => {
    const url = this.URL(`/sec/insider-trades/api/v2/presets`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getInsiderTradesTextSearch = (options: InsiderOptions): SafePromise<InsiderTradeSearchResult> => {
    const url = this.URL(`/sec/insider-trades/api/v2/text-search`, { ...options });
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };

  getInsiderTradesNetWorth = (cik: string): SafePromise<InsiderTradesNetWorthResult> => {
    const url = this.URL(`/sec/insider-trades/api/v2/net-worth/${cik}`);
    return this.get(url, { allowsAnonymousAuth: true, credentials: 'same-origin' });
  };
}

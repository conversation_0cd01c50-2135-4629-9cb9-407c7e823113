export const getPropertyCodeByHost = (host: string): string => {
  const properties = ['es', 'fr', 'it', 'jp', 'kr', 'in', 'pro', 'widgets'];
  const subdomain = host?.split('.')?.[0];

  if (properties.includes(subdomain)) {
    return subdomain;
  }

  return 'en';
};
export class TrackingEnvironment {
  public static getName = () => 'benzinga-tracking';
  public static getEnvironment = (env: Record<string, string>) => {
    let productionKey = 'gno9YRq4V6rrQSRSROznvuX0U2RfWHP9';
    const hostname = window?.location?.hostname;

    if (hostname) {
      const propertyCode = getPropertyCodeByHost(hostname);
      if (propertyCode === 'in') {
        productionKey = 'rN4qRexCcu8RQnOyzH7yqJ3mx8lDKNuN';
      } else if (propertyCode === 'pro') {
        return {
          segmentKey: env['segmentKey'],
        };
      } else if (propertyCode === 'widgets') {
        productionKey = 'g66ovsyaMadCH5LWzJqgWeNKCCGEwdkN';
      }
    }

    return {
      segmentKey: process.env['NODE_ENV'] === 'production' ? productionKey : 'l29fv9xz3v',
    };
  };
}

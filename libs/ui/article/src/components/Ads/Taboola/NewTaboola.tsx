'use client';
import React from 'react';
import { runningClientSide } from '@benzinga/utils';
import { InView } from 'react-intersection-observer';
import Hooks from '@benzinga/hooks';

type Article = 'auto';
type TargetType = 'mix';
export type Mode =
  | 'thumbnails-a'
  | 'thumbnails-b'
  | 'thumbnails-a-infinite'
  | 'thumbnails-right'
  | 'widget'
  | 'thumbs-feed-01'
  | 'thumbnails-mq'
  | 'alternating-thumbnails-a';

interface AddSettings {
  article?: Article;
  container?: string;
  mode?: Mode;
  placement?: string;
  target_type?: TargetType;
  flush?: boolean;
  url?: string;
  other?: string;
}

const loadTaboolaScript = (key = 'benzinga-benzinga1', callback) => {
  if (!runningClientSide()) {
    return;
  }

  if (document.getElementById('taboola-loader')) {
    callback && callback();
    return;
  }

  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  (function (e, f, u) {
    e.async = true;
    e.src = u;
    e.id = 'taboola-loader';
    f.parentNode?.insertBefore(e, f);
    callback && callback();
  })(
    document.createElement('script'),
    document.getElementsByTagName('script')[0],
    `//cdn.taboola.com/libtrc/${key}/loader.js`,
  );
};

export interface NewTaboolaProps {
  unitKey?: string;
  url: string;
  id?: string;
  vizSensor?: boolean;
  mode?: Mode;
  placement?: string;
  container?: string;
  settings?: AddSettings;
}

export const NewTaboola: React.FC<NewTaboolaProps> = ({
  container,
  id,
  mode = 'thumbnails-a',
  placement,
  settings,
  unitKey = 'benzinga-benzinga1',
  url,
  vizSensor = false,
}) => {
  const [loaded, setLoaded] = React.useState(false);
  const [pageCounter, setPageCounter] = React.useState<number>(1);

  const unitSettings: AddSettings = React.useMemo(() => {
    return settings || { article: 'auto', url };
  }, [url, settings]);

  React.useEffect(() => {
    const counter = window['_taboola_page_counter']
      ? window['_taboola_page_counter']++
      : (window['_taboola_page_counter'] = 1);
    setPageCounter(counter);
    //addSettings({ flush: true });
  }, []);

  const containerId = container || `taboola-below-article-${id || pageCounter}`;

  const loadSettings = React.useCallback(() => {
    addSettings({
      container: containerId,
      mode: mode,
      placement: placement || 'Infinite Scroll Page Feed',
      target_type: 'mix',
    });
  }, [containerId, mode, placement]);

  const load = React.useCallback(() => {
    loadTaboolaScript(unitKey, () => {
      loadSettings();
    });
    setLoaded(true);
  }, [unitKey, loadSettings]);

  Hooks.useEffectDidMount(() => {
    if (!loaded && !vizSensor) {
      if (!window['_taboola']) {
        window['_taboola'] = [];
      }

      addSettings(unitSettings);
      load();
    }
  });

  const addSettings = (settings: AddSettings): void => {
    if (Array.isArray(window['_taboola'])) {
      window['_taboola'].push(settings);
    }
  };

  const loadTaboola = (isVisible: boolean) => {
    if (!loaded && isVisible) {
      if (!window['_taboola']) {
        window['_taboola'] = [];
      }

      addSettings(unitSettings);
      load();
    }
  };

  const placeholder = React.useMemo(() => {
    return <div className="taboola-placeholder" id={containerId} style={{ minHeight: '10px' }}></div>;
  }, [containerId]);

  if (!vizSensor) {
    return placeholder;
  }

  return (
    <InView onChange={isVisible => loadTaboola(isVisible)} rootMargin="0px 0px -2000px 0px">
      {placeholder}
    </InView>
  );
};

export default NewTaboola;

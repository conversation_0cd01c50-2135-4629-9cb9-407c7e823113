'use client';
import React, { useState } from 'react';
import Hooks from '@benzinga/hooks';
import { runningClientSide } from '@benzinga/utils';

type Article = 'auto';
type TargetType = 'mix';
export type TaboolaUnitMode = 'thumbnails-a' | 'alternating-thumbnails-a';

interface AddSettings {
  article?: Article;
  container?: string;
  mode?: TaboolaUnitMode;
  placement?: string;
  target_type?: TargetType;
  flush?: boolean;
}

export const loadTaboolaScript = (key = 'benzinga-network', callback) => {
  if (!runningClientSide()) {
    return;
  }

  (function (d, s, n) {
    const js = d.createElement(s) as HTMLScriptElement;
    js.id = n;
    js.src = `//cdn.taboola.com/libtrc/${key}/loader.js`;
    js.defer = true;
    js.onload = callback;
    document.head.appendChild(js);
  })(document, 'script', 'taboola-script');
};

interface TaboolaProps {
  key?: string;
  mode?: TaboolaUnitMode;
}

export const Taboola: React.FC<TaboolaProps> = ({ key = 'benzinga-network', mode = 'thumbnails-a' }) => {
  const [loaded, setLoaded] = useState(false);

  const load = () => {
    loadTaboolaScript(key, () => {
      loadSettings();
      loadFeed();
    });
    setLoaded(true);
  };

  Hooks.useEffectDidMount(() => {
    if (!loaded) {
      if (!window['_taboola']) {
        window['_taboola'] = [];
      }

      addSettings({ article: 'auto' });
      load();
    }
  });

  const addSettings = (settings: AddSettings): void => {
    if (Array.isArray(window['_taboola'])) {
      window['_taboola'].push(settings);
    }
  };

  const loadSettings = () => {
    addSettings({
      container: 'taboola-below-article-thumbnails',
      mode,
      placement: 'Below Article Thumbnails',
      target_type: 'mix',
    });
  };

  const loadFeed = () => {
    addSettings({ flush: true });
  };

  return <div className="taboola-placeholder" id="taboola-below-article-thumbnails"></div>;
};

import React from 'react';
import Hooks from '@benzinga/hooks';
import { scrollDelay } from '@benzinga/frontend-utils';
import { runningServerSide } from '@benzinga/utils';
import { Mode } from './Ads';
import { isTaboolaAllowedToShow } from '@benzinga/article-manager';

const NewTaboola = React.lazy(() => import('./Ads/Taboola/NewTaboola'));

export interface TaboolaSettings {
  placementMethod?: 'interval' | 'localStorage' | 'below-article';
  unitMode?: Mode;
  unitPlacement?: string;
  unitId?: string;
  unitKey?: string;
}

interface BelowArticlePartnerAdBlockI {
  taboolaSettings?: TaboolaSettings;
  articleIndex?: number;
  id: string | number;
  url: string;
}

export const GetBelowArticlePartnerAdBlock = ({
  articleIndex,
  //id,
  taboolaSettings,
  url,
}: BelowArticlePartnerAdBlockI) => {
  const [shouldRenderPartnerAd, setShouldRenderPartnerAd] = React.useState<boolean>(false);

  Hooks.useEffectDidMount(() => {
    const loadPartnerBlocks = () => {
      scrollDelay(700, () => setShouldRenderPartnerAd(true));
    };

    if (document.readyState === 'complete') {
      loadPartnerBlocks();
    } else {
      window.addEventListener('load', loadPartnerBlocks);
    }
  });

  if (runningServerSide()) return null;
  if (!shouldRenderPartnerAd) return null;

  // const randomRotate = Math.random() >= 0.8;
  const isTaboolaAllowedToShowInterval = (articleIndex, interval) => {
    return articleIndex === 0 || articleIndex % interval === 0;
  };

  const shouldRenderTaboola =
    taboolaSettings?.placementMethod === 'localStorage'
      ? isTaboolaAllowedToShow(articleIndex ?? 0)
      : taboolaSettings?.placementMethod === 'interval'
        ? isTaboolaAllowedToShowInterval(articleIndex, 3)
        : taboolaSettings?.placementMethod === 'below-article';

  return (
    <>
      {/*<SmartAssetWidget />*/}

      {shouldRenderTaboola && (
        <div className="taboola-wrapper overflow-hidden">
          <NewTaboola
            id={taboolaSettings?.unitId || 'taboola-placement-0'}
            mode={taboolaSettings?.unitMode}
            placement={taboolaSettings?.unitPlacement}
            unitKey={taboolaSettings?.unitKey}
            url={url}
            vizSensor={false}
          />
        </div>
      )}
    </>
  );
};

'use client';
import React, { Suspense, useMemo } from 'react';
import styled from '@benzinga/themetron';
import Image from 'next/image';

import {
  ArticleData,
  authorTypeGenerator,
  calculateReadTime,
  Campaigns,
  getCanonicalUrl,
  getPrimaryTickers,
  isSponsoredArticle,
} from '@benzinga/article-manager';

import { ShareButtons } from '@benzinga/ui';
import Hooks, { NoFirstRender } from '@benzinga/hooks';
import { checkDeviceType, DeviceType } from '@benzinga/device-utils';

import { ErrorBoundary } from '@benzinga/core-ui';

import { DateContent } from './DateContent';
import { CommentButton } from './CommentButton';
import type { Mode } from './Ads';

import { truncate } from '@benzinga/utils';
import { IsUserPaywalledReturn } from '@benzinga/user-context';
import { ArticleFeaturedTicker } from '../entities';
import { DarkKeyPoints } from './KeyPoints';
import { NewAuthorContent } from './NewAuthorContent';
import { ArticleLayoutHeader as ArticleLayoutEditorBox } from './ArticleLayoutHeader';
import { NodeLayout } from '@benzinga/content-manager';
import { AdvertiserDisclosure } from './AdvertiserDisclosure';
import { whitelistedAuthors } from './ArticleLayoutMain';
import { RankingDetail } from '@benzinga/quotes-manager';
import { ArticleFeaturedTickersList } from './ArticleFeaturedTickersList';
import { BzImage } from '@benzinga/image';

// const ArticleFeaturedTickersList = React.lazy(() => import('./ArticleFeaturedTickersList'));

const KeyPoints = React.lazy(() => import('./KeyPoints'));
const ReadInAppButton = React.lazy(() => import('./ReadInAppButton'));
const GoogleNewsButton = React.lazy(() => import('./GoogleNewsButton'));
const AppleNewsButton = React.lazy(() => import('./AppleNewsButton'));

const CallToActionForm = React.lazy(() =>
  import('@benzinga/forms-ui').then(module => {
    return { default: module.CallToActionForm };
  }),
);

export interface TaboolaSettings {
  placementMethod?: 'interval' | 'localStorage' | 'below-article';
  unitMode?: Mode;
  unitKey?: string;
}

interface ArticleLayoutHeaderProps {
  articleData: ArticleData;
  campaigns?: Campaigns | null;
  deviceType: DeviceType | null;
  featuredTickers: ArticleFeaturedTicker[];
  googleNewsUrlKey: 'benzinga' | 'benzingaIndia';
  headerRef?: (node: HTMLDivElement | null) => void;
  hideTopPanel?: boolean;
  isBot: boolean;
  isDraft: boolean;
  isEditor: boolean;
  isNotPaywalled?: boolean;
  isPaywallActive?: boolean;
  isTemplate?: boolean;
  paywall: IsUserPaywalledReturn;
  showAdvertiserDisclosure: boolean;
  showCommentButton?: boolean;
  toggleCommentsDrawer?: () => void;
  baseUrl?: string;
  layout?: NodeLayout | null;
  rankingData?: RankingDetail | null;
}

const SHARE_BUTTONS_STYLES = {
  bgStyle: {
    fill: '#1E334B',
  },
  borderRadius: 1,
  iconFillColor: '#3F83F8',
};

export const NewArticleLayoutHeader: React.FC<ArticleLayoutHeaderProps> = React.memo(
  ({
    articleData,
    baseUrl,
    campaigns,
    deviceType,
    featuredTickers,
    googleNewsUrlKey,
    headerRef,
    hideTopPanel = false,
    isBot = true,
    isDraft = false,
    isEditor,
    isNotPaywalled,
    isPaywallActive,
    isTemplate = false,
    layout,
    rankingData,
    showAdvertiserDisclosure = false,
    toggleCommentsDrawer,
  }) => {
    const isApple = Hooks.useHydrate(checkDeviceType().isApple(), false);
    const isSponsored = isSponsoredArticle(articleData);

    const createdAt = articleData.createdAt ? new Date(articleData.createdAt).toISOString() : null;
    const contentType = articleData?.type;

    const keyItems = useMemo(() => {
      return [...(articleData.keyItems?.map(item => item.value).filter(item => item?.length) || [])];
    }, [articleData.keyItems]);

    const articleUrl = getCanonicalUrl(articleData);

    const tickers = useMemo(() => {
      return getPrimaryTickers(articleData?.tickers);
    }, [articleData?.tickers]);

    const authorName =
      articleData?.author?.firstname && articleData?.author?.lastname
        ? `${articleData?.author.firstname} ${articleData?.author.lastname}`
        : articleData?.author?.name || articleData?.name;

    const title = articleData?.isHeadline ? truncate(articleData.title ?? '', 45) : articleData.title;

    const financialsFeaturedTickers = useMemo(() => {
      return {
        down: Number(featuredTickers?.[0]?.analysis?.fundamentals?.down),
        up: Number(featuredTickers?.[0]?.analysis?.fundamentals?.up),
      };
    }, [featuredTickers]);

    const technicalsFeaturedTickers = useMemo(() => {
      return {
        down: Number(featuredTickers?.[0]?.analysis?.technicals?.down),
        up: Number(featuredTickers?.[0]?.analysis?.technicals?.up),
      };
    }, [featuredTickers]);

    const keyPointsArticleData = useMemo(() => {
      return {
        nodeId: articleData.nodeId,
        url: articleData.url,
      };
    }, [articleData.nodeId, articleData.url]);

    return (
      <Container $isTemplate={isTemplate} className="article-layout-main-header flex flex-col" ref={headerRef}>
        {!isTemplate && (
          <ArticleLayoutEditorBox
            articleData={articleData}
            baseUrl={baseUrl}
            isEditor={isEditor}
            isMobile={deviceType === 'mobile'}
            layout={layout}
            useNewTemplate={true}
          />
        )}
        <div className="flex relative overflow-hidden">
          {!isTemplate && (
            <Image
              alt=""
              className="absolute inset-0 w-full h-full z-0"
              fill
              priority
              sizes="(max-width: 1200px) 100vw, 1300px"
              src="/next-assets/images/article/article-header-background-image.webp"
              style={{ objectFit: 'cover' }}
            />
          )}
          <div className="article-layout-main-header-content flex w-full gap-4 p-4">
            <ContentContainer className="flex flex-col w-full h-full">
              {articleData.primaryImage?.url && (
                <>
                  <BzImage
                    alt=""
                    className="absolute inset-0 w-full h-full z-0"
                    fetchPriority="high"
                    height={600}
                    layout="fill"
                    objectFit="cover"
                    preload={true}
                    src={articleData.primaryImage.url}
                    width={1300}
                  />
                  <div className="absolute inset-0 bg-[rgba(25,41,64,0.85)] opacity-75 z-[1]" />
                </>
              )}
              <div className="content-wrapper">
                <div className="flex flex-col w-full items-start lg:flex-row">
                  <ErrorBoundary name="article-layout-main-date-content">
                    <Suspense fallback={<div />}>
                      <DateContent
                        created={createdAt ?? ''}
                        isDraft={isDraft}
                        readTime={calculateReadTime(articleData.body)}
                      />
                    </Suspense>
                  </ErrorBoundary>
                </div>
                {!!showAdvertiserDisclosure && <AdvertiserDisclosure slug={layout?.disclosure_slug} />}

                <h1 className="article-title text-2xl md:text-4xl text-white font-normal my-4">{title}</h1>

                {!hideTopPanel && (
                  <div className="flex flex-col w-full items-start lg:flex-row">
                    <ErrorBoundary name="article-layout-main-author-content">
                      <Suspense fallback={<div />}>
                        <NewAuthorContent
                          authorLink={articleData.author?.profileUrl}
                          authorName={authorName}
                          authorType={authorTypeGenerator(
                            articleData.author?.byLine ?? '',
                            showAdvertiserDisclosure,
                            contentType,
                          )}
                          authorUid={articleData.author?.uid}
                          className="flex flex-grow"
                          isTemplate={isTemplate}
                          twitter={articleData.author?.twitter}
                        />
                      </Suspense>
                    </ErrorBoundary>
                  </div>
                )}

                <div className="mt-auto">
                  <Suspense fallback={<div />}>
                    {keyItems && keyItems.length > 0 && (
                      <DarkKeyPoints className="key-points-wrapper hidden md:block">
                        <KeyPoints
                          article={keyPointsArticleData}
                          data={keyItems}
                          dynamicKeyPoint={campaigns?.top}
                          isPaywalled={
                            !isBot &&
                            !showAdvertiserDisclosure &&
                            !whitelistedAuthors.includes(authorName) &&
                            isPaywallActive &&
                            !isNotPaywalled &&
                            !isSponsored
                          }
                          trackImpression={true}
                        />
                      </DarkKeyPoints>
                    )}
                  </Suspense>

                  <Suspense fallback={<div className="h-[83.9px] w-full mt-4 [@media(min-width:482px)]:h-[40px]" />}>
                    {articleData.title && (
                      <div
                        className="flex flex-col h-[83.9px] w-full gap-2 mt-4
                [@media(min-width:482px)]:flex-row
                [@media(min-width:482px)]:h-[40px]
                lg:ml-auto"
                      >
                        <ShareButtons
                          buttonStyle={SHARE_BUTTONS_STYLES}
                          className="flex"
                          tickers={tickers}
                          title={articleData.title}
                          url={articleUrl}
                          utmSource="articleShare"
                        />
                        <div className="flex gap-3 md:ml-auto">
                          <div className="hidden md:flex">
                            <NoFirstRender fallback={<div className="h-[40px] w-[128px]" />}>
                              {googleNewsUrlKey === 'benzingaIndia' ? (
                                <GoogleNewsButton urlKey={googleNewsUrlKey} />
                              ) : !showAdvertiserDisclosure && isApple ? (
                                <AppleNewsButton />
                              ) : deviceType === 'mobile' ? (
                                <ReadInAppButton />
                              ) : (
                                <GoogleNewsButton />
                              )}
                            </NoFirstRender>
                          </div>
                          <CommentButton
                            commentCount={articleData.commentCount}
                            toggleCommentsDrawer={toggleCommentsDrawer}
                          />
                        </div>
                      </div>
                    )}
                  </Suspense>
                </div>
              </div>
            </ContentContainer>

            {Array.isArray(featuredTickers) && featuredTickers.length > 0 ? (
              <div className="hidden md:flex flex-col gap-4 w-full max-w-[450px] z-[2]">
                <Suspense fallback={<h1 className="text-white">ArticleFeaturedTickersList12345!!!</h1>}>
                  <ArticleFeaturedTickersList
                    financials={financialsFeaturedTickers}
                    rankingData={rankingData}
                    technicals={technicalsFeaturedTickers}
                    tickers={featuredTickers}
                  />
                </Suspense>
              </div>
            ) : (
              <div className="hidden md:flex z-[2]">
                <React.Suspense>
                  <CallToActionForm
                    // hubspotFormId="************************************"
                    beehiivFormId="c03f46e3-b180-439f-8cf4-103cbf2ac567"
                    styleOption="secondary"
                    subtitle="Enter your email to get Benzinga's ultimate morning update: The PreMarket Activity Newsletter"
                    title="Beat the Market With Our Free Pre-Market Newsletter"
                  />
                </React.Suspense>
              </div>
            )}
          </div>
        </div>
      </Container>
    );
  },
);

NewArticleLayoutHeader.displayName = 'NewArticleLayoutHeader';

const Container = styled.div<{ $isTemplate?: boolean }>`
  background-color: ${({ $isTemplate }) => ($isTemplate ? '#FFFFFF' : '#192940')};

  .article-layout-main-header-content {
    max-width: 1300px;
    margin: 0 auto;
    flex-direction: column;
    align-items: flex-start;
  }

  .article-header-ticker-list {
    display: none;
  }

  .call-to-action-form-wrapper {
    display: none;
  }

  @media (min-width: 1020px) {
    .article-layout-main-header-content {
      flex-direction: row;
    }

    .article-header-ticker-list {
      display: block;
    }

    .call-to-action-form-wrapper {
      display: flex;

      .secondary-container {
        height: auto;
      }
    }
  }

  .call-to-action-container {
    .success-message-container {
      margin-top: auto;
    }
  }
`;

const ContentContainer = styled.div`
  position: relative;
  padding: 1rem;
  border-radius: 0.5rem;
  overflow: hidden;

  .content-wrapper {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .author-name {
    color: ${({ theme }) => theme.colorPalette.white};
  }

  .key-points {
    border-radius: 0.25rem;
    margin-top: auto;

    .key-points-header {
      color: #979fac;
    }
  }

  .comments-count-button {
    padding: 0.3rem 1.25rem;
  }

  .page-share-buttons {
    button {
      border-radius: 0.25rem;
      overflow: hidden;
    }
  }

  .advertiser-disclosure-wrapper {
    display: flex;
    flex-wrap: wrap;
    margin-top: 0;

    @media (min-width: 500px) {
      margin-top: -25px;
      display: block;
    }

    button {
      background: #2b4c5a;
      padding: 0.15rem 0.5rem;
      border-radius: 0.5rem;
      color: #4ac0ee;
    }
  }
`;

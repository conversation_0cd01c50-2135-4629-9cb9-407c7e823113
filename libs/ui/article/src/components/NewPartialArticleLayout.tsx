'use client';
import React, { useMemo } from 'react';
import styled from '@benzinga/themetron';

import {
  ArticleData,
  generatePreviewFromBody,
  getCanonicalUrl,
  //printProHeadlineArticleContent,
} from '@benzinga/article-manager';

import { Button, ButtonVariant } from '@benzinga/core-ui';

import { Mode } from './Ads';
import { truncate } from '@benzinga/utils';
import { NodeLayout } from '@benzinga/content-manager';
import { TrackingManager } from '@benzinga/tracking-manager';
import { SessionContext } from '@benzinga/session-context';

export interface TaboolaSettings {
  placementMethod?: 'interval' | 'localStorage' | 'below-article';
  unitMode?: Mode;
  unitKey?: string;
}

export interface NewPartialArticleLayoutProps {
  articleID?: number;
  articleData: ArticleData;
  articleIndex?: number;
  deviceType?: string | null;
  articleScrollViewMoreLink?: string;
  googleNewsUrlKey?: string;
  isAmp?: boolean;
  isBot?: boolean;
  lastStory?: boolean;
  layout?: NodeLayout;
  loadMoreButtonVariant?: ButtonVariant;
  showAdvertiserDisclosure?: boolean;
  executeImpressionEvent?: boolean;
  executeReplaceHistory?: boolean;
  executePageviewEvent?: boolean;
  showCommentButton?: boolean;
  showFontAwesomeIcons?: boolean;
  showPartnerAd?: boolean;
  showWhatsAppIcon?: boolean;
  taboolaSettings?: TaboolaSettings;
  trackMeta?: boolean;
  isPaywallActive?: boolean;
  isNotPaywalled?: boolean;
}

export const NewPartialArticleLayout: React.FC<NewPartialArticleLayoutProps> = ({ articleData }) => {
  const session = React.useContext(SessionContext);

  const previewText = useMemo(() => {
    const result = articleData?.body ? generatePreviewFromBody(articleData?.body, 70) : articleData?.teaserText;
    return result;
  }, [articleData?.body, articleData?.teaserText]);

  const showAdvertiserDisclosure = articleData?.meta?.Flags?.ShowAdvertiserDisclosure;
  const title = articleData?.isHeadline ? truncate(articleData.title ?? '', 45) : articleData.title;
  const articleUrl = getCanonicalUrl(articleData ?? {});

  const handleReadMore = () => {
    const sponsored = articleData?.meta?.Flags?.ShowAdvertiserDisclosure;
    session.getManager(TrackingManager).trackCampaignEvent('click', {
      partner_id: (articleData?.meta?.Reach?.Disclosure?.tid || 0).toString(),
      sponsored: String(sponsored),
      unit_type: `article-infinite-scroll-${articleData?.nodeId}`,
    });
  };

  return (
    <Container className="partial-article-layout flex flex-col text-black">
      <div className="background-cover-pattern"></div>
      <div className="p-4">
        <a className="block" href={articleUrl} onClick={handleReadMore}>
          <div className="flex flex-col md:flex-row gap-6">
            {showAdvertiserDisclosure && (
              <div className="w-full flex justify-end md:hidden mb-[-11px] mt-[-2px]">
                <div className="advertiser-disclosure-label">Partner Disclosure</div>
              </div>
            )}
            {articleData?.image && (
              <div className="md:w-1/4 flex-shrink-0">
                <img alt={title} className="w-full h-32 md:h-28 object-cover rounded-lg" src={articleData.image} />
              </div>
            )}
            <div className={articleData?.image ? 'md:w-3/4 -mt-1' : 'w-full'}>
              {showAdvertiserDisclosure && (
                <div className="w-full justify-end hidden md:flex md:mt-[-15px]">
                  <div className="advertiser-disclosure-label">Partner Disclosure</div>
                </div>
              )}
              <h2 className="text-2xl line-clamp-3 font-normal">{title}</h2>
              <div
                className={articleData?.isHeadline ? undefined : 'preview-text'}
                // dangerouslySetInnerHTML={{
                //   __html: articleData?.isHeadline ? printProHeadlineArticleContent() : previewText || '',
                // }}
                dangerouslySetInnerHTML={{
                  __html: previewText || '',
                }}
              />
            </div>
          </div>
        </a>
      </div>
      {/* {showPartnerAd && taboolaSettings?.placementMethod !== 'below-article' && (
        <GetBelowArticlePartnerAdBlock
          articleIndex={articleIndex}
          id={articleData.nodeId ?? ''}
          taboolaSettings={taboolaSettings}
          url={articleUrl}
        />
      )} */}
      <Button
        as="a"
        className="w-full font-semibold read-more-button"
        href={articleUrl}
        onClick={handleReadMore}
        variant="flat-light-blue"
      >
        READ MORE
      </Button>
    </Container>
  );
};

const Container = styled.div`
  &.partial-article-layout {
    position: relative;
    background: ${({ theme }) => theme.colorPalette.gray50};
    color: ${({ theme }) => theme.colorPalette.gray700};
    margin-bottom: 2rem;
    border-radius: 0.5rem;
    overflow: hidden;

    .advertiser-disclosure-label {
      background: ${({ theme }) => theme.colorPalette.blue500};
      color: white;
      border-radius: 0.5rem;
      padding: 0.15rem 0.5rem;
      font-size: 14px;
      font-weight: 700;

      @media (min-width: 768px) {
        border-radius: 0 0 0.5rem 0.5rem;
      }
    }

    .background-cover-pattern {
      width: 100%;
      height: 100%;
      position: absolute;
      background-image: url('/next-assets/images/article/article-header-background-image.svg');
      top: -60px;
    }

    > * {
      position: relative;
      z-index: 2;
    }

    .article-date-wrap .date,
    .article-author-wrap {
      color: ${({ theme }) => theme.colorPalette.gray500};
    }

    .author-name {
      color: ${({ theme }) => theme.colorPalette.gray700};
    }

    .key-points {
      background-color: ${({ theme }) => theme.colorPalette.white};
      border: 1px solid ${({ theme }) => theme.colors.border};
      margin: 1rem 0;

      ul .bullet-point {
        color: ${({ theme }) => theme.colorPalette.gray700};
      }
    }

    .preview-text {
      margin-top: 0.5rem;
      color: ${({ theme }) => theme.colorPalette.gray900};
      a {
        color: ${({ theme }) => theme.colorPalette.gray900};
      }
      strong {
        font-weight: normal;
      }
    }

    .article-read-time {
      color: ${({ theme }) => theme.colorPalette.gray500};
    }

    .comments-count-button {
      border-color: ${({ theme }) => theme.colorPalette.blue500}0D;
      white-space: nowrap;

      @media (max-width: 500px) {
        padding: 0.4rem 0.95rem;
      }
    }
    .comments-count-icon-wrapper {
      color: ${({ theme }) => theme.colorPalette.gray500};
    }

    .read-in-app-button {
      height: 38px;
    }
  }
`;

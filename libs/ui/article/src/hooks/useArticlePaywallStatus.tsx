import { useCallback } from 'react';
import { ArticleData } from '@benzinga/article-manager';
import { IsUserPaywalledReturn } from '@benzinga/user-context';
import { getPaywallType, getAuthorName } from '../utils';
import { DeviceType } from '@benzinga/device-utils';
import { sophiManager } from '@benzinga/ads-utils';
import { usePaywallStatus, PaywallConfig } from './usePaywallStatus';
import { useScrollPaywallActivation, ScrollConfig } from './useScrollPaywallActivation';
import { MARKET_MOVING_EXCLUSIVES_CHANNEL_ID } from '../constants/paywall';

interface UseArticlePaywallStatusReturn {
  isNotPaywalled: boolean;
  isPaywallActive: boolean;
  setIsPaywallActive: (value: boolean) => void;
  paywall: IsUserPaywalledReturn;
  headerRef: (node: HTMLDivElement | null) => void;
}

export const useArticlePaywallStatus = (
  articleData: ArticleData,
  disablePaywall?: boolean,
  deviceType?: DeviceType | null,
  paywallConfig: PaywallConfig = {},
  scrollConfig: ScrollConfig = {},
): UseArticlePaywallStatusReturn => {
  const isChanneled = useCallback(
    (tids: number[]) => {
      return articleData?.channels?.some(channel => tids.includes(channel?.tid));
    },
    [articleData?.channels],
  );

  const authorName = getAuthorName(articleData);
  const isNotPaywalled = isChanneled([MARKET_MOVING_EXCLUSIVES_CHANNEL_ID]);

  const { paywall, shouldShowPaywall } = usePaywallStatus(
    {
      action: 'com/read',
      disableArtificialLoading: true,
      paywallType: getPaywallType(articleData),
      resource: 'unlimited-articles',
      ...paywallConfig,
    },
    {
      authorName,
      customExclusions: [disablePaywall || false],
      isMarketMovingExclusive: isNotPaywalled,
    },
  );

  const { headerRef, isPaywallActive, setIsPaywallActive } = useScrollPaywallActivation(
    shouldShowPaywall,
    deviceType,
    () => {
      const section = articleData?.channels?.[0]?.name || '';
      sophiManager.trackWallHit('paywall', section);
    },
    scrollConfig,
  );

  return {
    headerRef,
    isNotPaywalled,
    isPaywallActive,
    paywall,
    setIsPaywallActive,
  };
};

import { useMemo } from 'react';
import { IsUserPaywalledReturn, useIsUserPaywalled } from '@benzinga/user-context';

export interface PaywallConfig {
  action?: string;
  resource?: string;
  paywallType?: 'soft' | 'soft-hard' | 'hard';
  disableArtificialLoading?: boolean;
}

export interface PaywallExclusions {
  authorName?: string;
  isMarketMovingExclusive?: boolean;
  isSponsored?: boolean;
  customExclusions?: boolean[];
}

export interface UsePaywallStatusReturn {
  paywall: IsUserPaywalledReturn;
  shouldShowPaywall: boolean;
}

/**
 * Abstract hook for paywall status that can be used across different content types
 * @param config - Paywall configuration
 * @param exclusions - Conditions that should exclude content from paywall
 * @returns Paywall status and computed shouldShowPaywall flag
 */
export const usePaywallStatus = (
  config: PaywallConfig = {},
  exclusions: PaywallExclusions = {},
): UsePaywallStatusReturn => {
  const {
    action = 'com/read',
    resource = 'unlimited-articles',
    paywallType = 'soft-hard',
    disableArtificialLoading = true,
  } = config;

  const paywall = useIsUserPaywalled(
    action,
    resource,
    paywallType,
    disableArtificialLoading,
    exclusions.authorName,
  );

  const shouldShowPaywall = useMemo(() => {
    // If paywall is not active, don't show
    if (!paywall.active) {
      return false;
    }

    // Check for various exclusions
    if (exclusions.isMarketMovingExclusive) {
      return false;
    }

    if (exclusions.isSponsored) {
      return false;
    }

    // Check custom exclusions (any true value excludes from paywall)
    if (exclusions.customExclusions?.some(exclusion => exclusion)) {
      return false;
    }

    return true;
  }, [
    paywall.active,
    exclusions.isMarketMovingExclusive,
    exclusions.isSponsored,
    exclusions.customExclusions,
  ]);

  return {
    paywall,
    shouldShowPaywall,
  };
};

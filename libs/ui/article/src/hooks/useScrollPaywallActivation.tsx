import { useCallback, useEffect, useState, useRef, startTransition } from 'react';
import { DeviceType } from '@benzinga/device-utils';

export interface ScrollConfig {
  thresholdPercentage?: number;
  excludeNavHeaderOnMobile?: boolean;
  navHeaderId?: string;
}

export interface UseScrollPaywallActivationReturn {
  isPaywallActive: boolean;
  setIsPaywallActive: (value: boolean) => void;
  headerRef: (node: HTMLDivElement | null) => void;
}

/**
 * Hook for managing scroll-based paywall activation
 * @param shouldActivate - Whether the paywall should be activated on scroll
 * @param deviceType - Current device type
 * @param onActivate - Callback when paywall is activated
 * @param config - Scroll configuration
 * @returns Paywall activation state and header ref
 */
export const useScrollPaywallActivation = (
  shouldActivate: boolean,
  deviceType?: DeviceType | null,
  onActivate?: () => void,
  config: ScrollConfig = {},
): UseScrollPaywallActivationReturn => {
  const {
    thresholdPercentage = 0.4,
    excludeNavHeaderOnMobile = true,
    navHeaderId = 'navigation-header',
  } = config;

  const [isPaywallActive, setIsPaywallActive] = useState(false);
  const headerRef = useRef<HTMLDivElement | null>(null);
  const scrollHandlerRef = useRef<(() => void) | null>(null);

  const setHeaderRef = useCallback((node: HTMLDivElement | null) => {
    if (node) {
      headerRef.current = node;
    }
  }, []);

  useEffect(() => {
    if (!shouldActivate || !headerRef.current) {
      return;
    }

    const handleScroll = () => {
      if (!headerRef.current) return;

      const navHeader = document.getElementById(navHeaderId);
      const navHeaderHeight = navHeader?.offsetHeight ?? 0;

      const headerRect = headerRef.current.getBoundingClientRect();
      const headerHeight = headerRect.height;
      const scrollPosition = window.scrollY;

      // Calculate scroll threshold based on configuration
      const navOffset = (deviceType === 'mobile' && excludeNavHeaderOnMobile) ? 0 : navHeaderHeight;
      const scrollThreshold = headerHeight * thresholdPercentage + navOffset;

      if (scrollPosition >= scrollThreshold) {
        startTransition(() => {
          setIsPaywallActive(true);
          onActivate?.();
        });
        
        // Remove scroll listener after activation
        if (scrollHandlerRef.current) {
          window.removeEventListener('scroll', scrollHandlerRef.current);
        }
      }
    };

    scrollHandlerRef.current = handleScroll;
    window.addEventListener('scroll', scrollHandlerRef.current);

    return () => {
      if (scrollHandlerRef.current) {
        window.removeEventListener('scroll', scrollHandlerRef.current);
      }
    };
  }, [shouldActivate, deviceType, thresholdPercentage, excludeNavHeaderOnMobile, navHeaderId, onActivate]);

  return {
    headerRef: setHeaderRef,
    isPaywallActive,
    setIsPaywallActive,
  };
};

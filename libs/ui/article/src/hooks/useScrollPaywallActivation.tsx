import { useCallback, useEffect, useState, useRef, startTransition } from 'react';
import { DeviceType } from '@benzinga/device-utils';

export interface ScrollConfig {
  thresholdPercentage?: number;
  excludeNavHeaderOnMobile?: boolean;
  navHeaderId?: string;
}

export interface UseScrollPaywallActivationReturn {
  isPaywallActive: boolean;
  setIsPaywallActive: (value: boolean) => void;
  headerRef: (node: HTMLDivElement | null) => void;
}

export const useScrollPaywallActivation = (
  shouldActivate: boolean,
  deviceType?: DeviceType | null,
  onActivate?: () => void,
  config: ScrollConfig = {},
): UseScrollPaywallActivationReturn => {
  const { excludeNavHeaderOnMobile = true, thresholdPercentage = 0.4 } = config;

  const [isPaywallActive, setIsPaywallActive] = useState(false);
  const headerRef = useRef<HTMLDivElement | null>(null);
  const scrollHandlerRef = useRef<(() => void) | null>(null);

  const setHeaderRef = useCallback((node: HTMLDivElement | null) => {
    if (node) {
      headerRef.current = node;
    }
  }, []);

  useEffect(() => {
    if (!shouldActivate || !headerRef.current) {
      return;
    }

    const handleScroll = () => {
      if (!headerRef.current) return;

      const navHeader = document.getElementById('navigation-header');
      const navHeaderHeight = navHeader?.offsetHeight ?? 0;

      const headerRect = headerRef.current.getBoundingClientRect();
      const headerHeight = headerRect.height;
      const scrollPosition = window.scrollY;
      const scrollThreshold = headerHeight * 0.4 + (deviceType === 'mobile' ? 0 : navHeaderHeight);

      if (scrollPosition >= scrollThreshold) {
        startTransition(() => {
          setIsPaywallActive(true);
          typeof onActivate === 'function' && onActivate();
        });

        if (scrollHandlerRef.current) {
          window.removeEventListener('scroll', scrollHandlerRef.current);
        }
      }
    };

    scrollHandlerRef.current = handleScroll;
    window.addEventListener('scroll', scrollHandlerRef.current);

    return () => {
      if (scrollHandlerRef.current) {
        window.removeEventListener('scroll', scrollHandlerRef.current);
      }
    };
  }, [shouldActivate, deviceType, thresholdPercentage, excludeNavHeaderOnMobile, onActivate]);

  return {
    headerRef: setHeaderRef,
    isPaywallActive,
    setIsPaywallActive,
  };
};

import { ArticleData } from '@benzinga/article-manager';

const isTagged = (article: ArticleData, tids: number[]) => {
  return article.tags?.some(tag => tids.includes(tag?.tid));
};

const isChanneled = (article: ArticleData, tids: number[]) => {
  return article.channels?.some(channel => tids.includes(channel?.tid));
};

export const getPaywallContentType = (article: ArticleData) => {
  if (isTagged(article, [931449])) return 'stock-of-the-day';
  if (isTagged(article, [866728])) return 'whisper-index';
  if (isChanneled(article, [68])) return 'analyst-color';
  if (isTagged(article, [944696])) return 'easy-income-portfolio';
  if (isTagged(article, [944719])) return 'perfect-stock-portfolio';
  // if (isTagged([938584])) return 'insider-report';
  return 'default-article';
};

export const getPaywallType = (article: ArticleData) => {
  const paywallTags = {
    'c-suite-buys-of-the-week': 943954,
    'easy-income-portfolio': 944696,
    'insider-report': 938584,
    interviews: 714112,
    mavens: 154244,
    'options-trade-of-the-day': 946635,
    'perfect-stock-portfolio': 944719,
    'stock-of-the-day': 931449,
    'stock-whisper-index': 866728,
    'under-the-radar': 943823,
  };

  if (isTagged(article, Object.values(paywallTags))) return 'hard';
  // || isChanneled(article, [16888, 68, 38, 22]
  return 'soft-hard';
};

/**
 * Extracts the author name from article data
 * @param articleData - The article data object
 * @returns The formatted author name or fallback
 */
export const getAuthorName = (articleData: ArticleData): string => {
  if (articleData?.author?.firstname && articleData?.author?.lastname) {
    return `${articleData.author.firstname} ${articleData.author.lastname}`;
  }
  return articleData?.author?.name || articleData?.name || '';
};

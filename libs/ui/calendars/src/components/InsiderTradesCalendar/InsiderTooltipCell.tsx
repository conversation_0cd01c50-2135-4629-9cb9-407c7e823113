'use client';
import React from 'react';

interface Props {
  rowIndex: number;
  value: {
    field: string;
    fieldVal?: number;
    tooltipLines: string;
  };
  eParentOfValue: any;
}

export default React.forwardRef<HTMLDivElement, Props>((props, ref) => {
  const cellRef = React.useRef(props.eParentOfValue);

  function getTooltipHeight() {
    const numOfLines = props.value.tooltipLines?.length;
    if (numOfLines === 1) {
      return 36;
    }

    return 28 * numOfLines;
  }

  const showTooltip = React.useCallback(() => {
    window.dispatchEvent(
      new MessageEvent('renderTooltip', {
        data: {
          cellRect: cellRef.current.getBoundingClientRect(),
          tooltipData: props?.value?.tooltipLines,
          tooltipHeight: getTooltipHeight(),
          type: 'show',
        },
      }),
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const hideTooltip = React.useCallback(() => {
    window.dispatchEvent(
      new MessageEvent('renderTooltip', {
        data: {
          type: 'hide',
        },
      }),
    );
  }, []);

  return (
    <div onMouseLeave={hideTooltip} onMouseOver={showTooltip} ref={ref}>
      {props?.value?.field}
    </div>
  );
});

'use client';
import React from 'react';
import subYears from 'date-fns/subYears';
import Layout from '../lib/components/Layout';
import ColumnsDefinition from './ColumnsDefs/ColumnsDef';
import ColumnsDefinitionV2 from './ColumnsDefs/ColumnsDefV2';
import NetWorthColumnsDefinition from './ColumnsDefs/NetWorthColumnsDef';
import { formatRequestDate } from '../lib/utils';
import { fetchInsiderTrades } from './Services';

import { ICalendarProps, IDateRange } from '../lib/interfaces';
import InsiderCustomTooltip from './InsiderCustomTooltip';

export const InsiderTradesCalendarColumnsDef = ColumnsDefinition;
export const InsiderTradesCalendarColumnsDefV2 = ColumnsDefinitionV2;
export const InsiderTradesNetWorthColumnsDef = NetWorthColumnsDefinition;

interface IInsiderTradesCalendarProps extends ICalendarProps {
  isGated?: boolean;
  isV2?: boolean;
  tableIndex?: number;
}

export const InsiderTradesCalendar: React.FC<IInsiderTradesCalendarProps> = ({
  calendar,
  calendarData,
  calendarType,
  hasStockTableStyling,
  height,
  hideFilters,
  initialData,
  initialDateRange,
  initialTicker,
  isGated = true,
  isV2 = false,
  leftSlot,
  onReady,
  tableIndex = 0,
}) => {
  const [loading, setLoading] = React.useState(false);
  const [tableData, setTableData] = React.useState(initialData || []);
  let dateRange: IDateRange;

  if (initialDateRange) {
    dateRange = initialDateRange;
  } else {
    dateRange = {
      date_from: formatRequestDate(subYears(new Date(), 5)),
      date_to: formatRequestDate(new Date()),
    };
  }

  React.useEffect(() => {
    if (calendarData?.serverSide) {
      setTableData(initialData);
    }
  }, [initialData, calendarData]);

  function onTableReady() {
    if (initialData) {
      setTableData(initialData);
    } else {
      const params = {
        'parameters[date_from]': dateRange.date_from,
        'parameters[date_to]': dateRange.date_to,
      };
      if (initialTicker) {
        params['parameters[tickers]'] = initialTicker;
      }
      fetchData(params);
    }
    if (onReady) {
      setTimeout(() => onReady());
    }
  }

  async function fetchData(filterParams: any) {
    const query = {
      dateRange: {
        date_from: filterParams['parameters[date_from]'],
        date_to: filterParams['parameters[date_to]'],
      },
      symbol: filterParams['parameters[tickers]'],
    };
    setLoading(true);
    const insiderTradesSummary = await fetchInsiderTrades(query);
    if (insiderTradesSummary.filings) {
      setTableData(insiderTradesSummary.filings);
    }
    setLoading(false);
  }

  return (
    <Layout
      additionalGridOptions={
        isV2
          ? {
              defaultColDef: {
                tooltipComponent: InsiderCustomTooltip,
              },
              rowClassRules: {
                'row-green': params => params.api.getValue('traded_shares', params.node) > 0,
                'row-red': params => params.api.getValue('traded_shares', params.node) < 0,
              },
            }
          : undefined
      }
      calendar={calendar}
      calendarData={calendarData}
      calendarType={calendarType}
      columnDefs={calendarData?.serverSide?.columnDef}
      enableSearch
      hasStockTableStyling={hasStockTableStyling}
      height={height}
      hideFilters={hideFilters}
      initialDateRange={dateRange}
      isGated={isGated}
      leftSlot={leftSlot}
      onFilterChanged={fetchData}
      onTableReady={onTableReady}
      tableData={tableData}
      tableIndex={tableIndex}
      tableLoading={loading}
      ticker={initialTicker}
    />
  );
};

import { getNumberSuffix } from '@benzinga/utils';
import { DateTime as LuxonDateTime } from 'luxon';
import { AnalystRatingCalendar, AnalystRatingsColumnsDef, fetchAnalystRatingsData } from './AnalystRatingCalendar';
import { ConferenceCallCalendar, ConferenceCallCalendarDef, fetchConferenceCallData } from './ConferenceCallCalendar';
import { DividendsCalendarColumnsDef, fetchDividendsData, DividendsCalendar } from './DividendsCalendar';
import { EarningsCalendarColumnsDefinition, fetchEarningsData, EarningsCalendar } from './EarningsCalendar';
import { EconomicsColDef, fetchEconomicsData, EconomicsCalendar } from './EconomicsCalendar';
import { GuidanceCalendarColumnsDef, fetchGuidanceData, GuidanceCalendar } from './GuidanceCalendar';
import { IPOCalendar, IPOColumnsDef, fetchIPOData } from './IPOCalendar';
import { CalendarDataI, CalendarType, IDateRange } from './lib/interfaces';
import {
  formatInterval,
  formatIntervalLastOpenMarketDay,
  formatIntervalQueryParams,
  getLastOpenMarketDay,
} from './lib/utils';
import { MACalendar, MACalendarColumnsDef, fetchMAData } from './MACalendar';
import { OptionsCalendar, OptionsCalendarColumnsDef, fetchOptionsData } from './OptionsCalendar';
import { SPACColumnsDefinition, fetchSPAC, SpacCalendar } from './SpacCalendar';
import { StockSplitsCalendar, StockSplitsColumnsDef, fetchStockSplitsData } from './StockSplitsCalendar';
import { Ratings } from '@benzinga/calendar-manager';
import {
  InsiderTradesCalendar,
  InsiderTradesCalendarColumnsDef,
  InsiderTradesCalendarColumnsDefV2,
  InsiderTradesNetWorthColumnsDef,
} from './InsiderTradesCalendar';
import { NewsFeedQueryParams } from '@benzinga/basic-news-manager';
import { ShortInterestCalendar, ShortInterestCalendarColumnDef } from './ShortInterestCalendar';
import { FDACalendar, FDAColumnsDef, fetchFDAData } from './FDACalendar';

const jsDateString = new Date().toISOString().split('T')[0];
const now = LuxonDateTime.fromFormat(jsDateString, 'yyyy-MM-dd');
const todayDateStroke = now.toFormat('MMM d').concat(`${getNumberSuffix(now.day)}`);

const SevenDaysBeforeInterval: IDateRange = formatIntervalLastOpenMarketDay(now.minus({ days: 7 }), now);
const SevenDaysAfterInterval: IDateRange = formatIntervalLastOpenMarketDay(now, now.plus({ days: 7 }));

const ThirtyDaysBeforeInterval: IDateRange = formatIntervalLastOpenMarketDay(now.minus({ days: 30 }), now);
const ThirtyDaysAfterInterval: IDateRange = formatIntervalLastOpenMarketDay(now, now.plus({ days: 30 }));

const TwoMonthsAfterInterval: IDateRange = formatIntervalLastOpenMarketDay(now, now.plus({ months: 2 }));

const OneMonthBefore: IDateRange = formatIntervalLastOpenMarketDay(now.minus({ months: 1 }), now);
const OneMonthAfter: IDateRange = formatIntervalLastOpenMarketDay(now, now.plus({ months: 1 }));

const TwoMonthsBefore: IDateRange = formatIntervalLastOpenMarketDay(now.minus({ months: 2 }), now);

const FiveYearsBeforeInterval: IDateRange = formatIntervalLastOpenMarketDay(now.minus({ years: 5 }), now);

const sevenDaysBeforeDateStroke = now.minus({ days: 7 });
const oneMonthBeforeDateStroke = now.minus({ months: 1 });
const thirtyDaysBeforeDateStroke = now.minus({ days: 30 });
const twoMonthsBeforeDateStroke = now.minus({ months: 2 });
const fiveYearsBeforeDateStroke = now.minus({ years: 5 });

const formatCalendarMetaTitle = (calendarName: string, asOfDate: LuxonDateTime, suffix?: string) => {
  try {
    const todayDateString = now.toFormat('yyyy-MM-dd');
    const asOfDateString = asOfDate.toFormat('yyyy-MM-dd');
    asOfDate = getLastOpenMarketDay(now).date;
    let result = `${calendarName} as of ${asOfDate.toFormat('MMM d').concat(`${getNumberSuffix(asOfDate.day)}`)}`;
    const lessThanLastOpenMarketDay = asOfDateString <= (getLastOpenMarketDay(now).dateRange.date_from as string);
    if (
      asOfDateString === todayDateString &&
      todayDateString !== getLastOpenMarketDay(now).dateRange.date_from &&
      lessThanLastOpenMarketDay
    ) {
      result = result + ` | ${suffix ? suffix : calendarName?.replace('Calendar', '')} Today`;
    }
    return result;
  } catch {
    return calendarName;
  }
};

const AnalystRatingsCalendarData: CalendarDataI = {
  calendar: {
    interval: SevenDaysBeforeInterval,
  },
  calendarDate: new Date(),
  component: AnalystRatingCalendar,
  description:
    'Analyst ratings are quantitative and qualitative analysis of a stock by Wall Street stock rating analysts. Stock ratings consist of expected future growth, current stock valuation and macroeconomic trends.',
  name: 'Analyst Stock Ratings',
  news: {
    params: {
      channels: ['Analyst Ratings'],
      displayOutput: 'abstract',
      pageSize: 10,
      type: 'stories',
    },
    title: 'Recent Analyst Stock Rating News',
  },
  pageId: 90498,
  routes: {
    downgrades: {
      calendarDate: new Date(),
      component: AnalystRatingCalendar,
      description:
        'Analyst downgrades are often a bearish signal for a stock. When an analyst downgrades a stock it means the stock rating is lower than it previously was and the analyst likely does not recommend people buy the stock.',
      name: 'Analyst Ratings',
      news: {
        params: {
          channels: ['Downgrades'],
          displayOutput: 'abstract',
          pageSize: 10,
          type: 'stories',
        },
        title: 'Recent Analyst Stock Rating Downgrades News',
      },
      pageId: 101828,
      title: 'Analyst Stock Ratings Downgrades',
    },
    initiations: {
      component: AnalystRatingCalendar,
      name: 'Analyst Ratings',
      news: {
        params: {
          channels: ['Initiation'],
          displayOutput: 'abstract',
          pageSize: 10,
          type: 'stories',
        },
        title: 'Recent Analyst Stock Rating Initiation News',
      },
      pageId: 101833,
      title: 'Analyst Stock Ratings Initiations',
    },
    upgrades: {
      calendarDate: new Date(),
      component: AnalystRatingCalendar,
      description:
        "Analyst upgrades are typically bullish for a stock. When there is a stock upgrade, the analysts who rate the stock feel better about the company's future and typically either recommend someone holds or buys the stock.",
      name: 'Analyst Ratings',
      news: {
        params: {
          channels: ['Upgrades'],
          displayOutput: 'abstract',
          pageSize: 10,
          type: 'stories',
        },
        title: 'Recent Analyst Stock Rating Upgrade News',
      },
      pageId: 101830,
      title: 'Analyst Stock Ratings Upgrades',
    },
  },
  seo: {
    description:
      'From "buy" and "sell" ratings to "equal weight" and "outperform" ratings, you want to pay attention to analyst ratings as a trader or investor.',
  },
  separateTitle: true,
  serverSide: {
    columnDef: AnalystRatingsColumnsDef,
    fetchData: (options?: any) => {
      const params = {
        pagesize: 1000,
        ...formatIntervalQueryParams(SevenDaysBeforeInterval),
        ...(options.params && options.params),
      };

      return fetchAnalystRatingsData(params, false).then((items: Ratings[]) => {
        if (options?.tab) {
          const initiates: Ratings[] = [];
          const downgrades: Ratings[] = [];
          const upgrades: Ratings[] = [];

          if (Array.isArray(items)) {
            items.forEach(item => {
              if (item.action_company === 'Initiates Coverage On') {
                initiates.push(item);
              } else if (item.action_company === 'Downgrades') {
                downgrades.push(item);
              } else if (item.action_company === 'Upgrades') {
                upgrades.push(item);
              }
            });
          }

          switch (options.tab) {
            case 'upgrades':
              return upgrades;
            case 'initiations':
              return initiates;
            case 'downgrades':
              return downgrades;
            case 'all':
            default:
              return items;
          }
        } else {
          return items;
        }
      });
    },
  },
  showProCTA: true,
  title: "Today's Analyst Stock Ratings | Upgrades, Downgrades",
};

export const Calendars: { [key in CalendarType]: CalendarDataI } = {
  'analyst-predictions': {
    ...AnalystRatingsCalendarData,
    name: 'Analyst Predictions',
    news: AnalystRatingsCalendarData.news as { title: string; params: NewsFeedQueryParams },
    pageId: 90498,
    routes: {
      downgrades: {
        ...(AnalystRatingsCalendarData?.routes?.downgrades as CalendarDataI),
        title: 'Stock Ratings - Downgrades',
      },
      initiations: {
        ...(AnalystRatingsCalendarData?.routes?.initiations as CalendarDataI),
        title: 'Stock Ratings - Initiations',
      },
      upgrades: {
        ...(AnalystRatingsCalendarData?.routes?.upgrades as CalendarDataI),
        title: 'Stock Ratings - Upgrades',
      },
    },
    seo: {
      canonical: 'https://www.benzinga.com/analyst-stock-ratings',
      description:
        'From "buy" and "sell" ratings to "equal weight" and "outperform" ratings, you want to pay attention to analyst ratings as a trader or investor.',
    },
    title: 'Stock Ratings',
  },
  'analyst-ratings': {
    ...AnalystRatingsCalendarData,
    seo: {
      ...AnalystRatingsCalendarData.seo,
      canonical: 'https://www.benzinga.com/analyst-stock-ratings',
    },
  },
  'conference-calls': {
    calendar: {
      interval: OneMonthAfter,
    },
    component: ConferenceCallCalendar,
    injectTickersLogos: true,
    name: 'Conference Calls',
    news: {
      params: {
        channels: ['Earnings'],
        pageSize: 10,
      },
      title: 'Recent Earnings News',
    },
    pageId: 101381,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/conference-calls',
      description: 'Stay up to date with upcoming conference calls for publicly traded companies.',
    },
    serverSide: {
      columnDef: ConferenceCallCalendarDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(OneMonthAfter),
          ...(options.params && options.params),
        };

        return fetchConferenceCallData(params, false, 1000);
      },
    },
    title: formatCalendarMetaTitle(
      'Conference Calls',
      LuxonDateTime.fromFormat(OneMonthAfter?.date_from as string, 'yyyy-MM-dd'),
    ),
  },
  dividends: {
    calendar: {
      interval: SevenDaysAfterInterval,
    },
    component: DividendsCalendar,
    description: `The table below is a list view calendar of stocks with ex-dividend dates as of ${todayDateStroke}. The table is sorted by dividends today. In order for an investor to receive a dividend, they must own a stock, ETF or mutual fund by the ex-dividend date.`,
    name: 'Dividend',
    news: {
      params: {
        channels: ['Dividends'],
        pageSize: 10,
      },
      title: 'Recent Dividend News',
    },
    pageId: 90241,
    seo: {
      canonical: 'https://www.benzinga.com/dividends',
      description:
        "Learn dividend-paying stocks and pay dates with the latest, most up-to-date market information from Benzinga's dividend calendar",
    },
    serverSide: {
      columnDef: DividendsCalendarColumnsDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(SevenDaysAfterInterval),
          'parameters[date_sort]': 'record:asc',
          ...(options.params && options.params),
        };

        return fetchDividendsData(params, false, 1000);
      },
    },
    title: formatCalendarMetaTitle('Dividend Calendar', getLastOpenMarketDay(now).date, 'Dividends'),
  },
  earnings: {
    calendar: {
      fallbackInterval: ThirtyDaysAfterInterval,
      interval: getLastOpenMarketDay(now).dateRange,
    },
    campaignifyUTM: 'campaignify-earnings',
    campaignifyVariant: 'claim-your-free-benzinga-pro-access',
    component: EarningsCalendar,
    description:
      'The table below is a calendar list view of publicly traded stocks that have released their earnings reports. This earnings calendar is updated daily, so if the company you are looking for is not here today, either adjust the date range wider or come back and check tomorrow.',
    injectTickersLogos: true,
    name: 'Earnings',
    news: {
      params: {
        channels: ['Earnings'],
        pageSize: 10,
      },
      title: 'Recent Earnings News',
    },
    pageId: 99752,
    seo: {
      canonical: 'https://www.benzinga.com/earnings',
      description:
        "Keep up with companies' quarterly and annual earnings, along with the latest EPS estimates from each company with Benzinga",
    },
    separateTitle: true,
    serverSide: {
      columnDef: EarningsCalendarColumnsDefinition,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(getLastOpenMarketDay(now).dateRange),
          ...(options.params && options.params),
        };
        return fetchEarningsData(params, 1000);
      },
    },
    showProCTA: true,
    title: formatCalendarMetaTitle('Earnings Calendar', getLastOpenMarketDay(now).date),
  },
  economic: {
    calendar: {
      fallbackInterval: SevenDaysAfterInterval,
      interval: SevenDaysBeforeInterval,
    },
    component: EconomicsCalendar,
    description: '',
    name: 'Economic',
    news: {
      params: {
        channels: ['Economics'],
        pageSize: 10,
      },
      title: 'Recent Economic News',
    },
    pageId: 90254,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/economic',
      description:
        'Stay informed about upcoming economic events and indicators with our comprehensive economic calendar.',
    },
    serverSide: {
      columnDef: EconomicsColDef,
      fetchData: options => {
        const params = {
          ...formatIntervalQueryParams(SevenDaysBeforeInterval),
          ...(options?.params || {}),
        };

        return fetchEconomicsData(params, 1000);
      },
    },
    title: formatCalendarMetaTitle('Economic Calendar', getLastOpenMarketDay(now).date),
  },
  fda: {
    calendar: {
      interval: ThirtyDaysAfterInterval,
    },
    component: FDACalendar,
    name: 'FDA',
    news: {
      params: {
        pageSize: 5,
        topics: ['FDA Approvals'],
      },
      title: 'Recent FDA Approvals',
    },
    pageId: 105610,
    seo: {
      canonical: 'https://www.benzinga.com/fda-calendar',
      description:
        "Updated daily, the FDA calendar gives you insight into FDA actions on companies and upcoming actions the FDA is expected to take. Benzinga's FDA calendar shows historical FDA data, upcoming dates that companies will be impacted by the FDA and ranges of dates.",
    },
    serverSide: {
      columnDef: FDAColumnsDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(ThirtyDaysAfterInterval),
          ...(options.params && options.params),
        };

        return fetchFDAData(params, 'historical', 1000);
      },
    },
    title: 'FDA Calendar and Recent FDA News',
  },
  guidance: {
    calendar: {
      interval: OneMonthBefore,
    },
    component: GuidanceCalendar,
    name: 'Guidance',
    news: {
      params: {
        channels: ['Guidance'],
        pageSize: 10,
      },
      title: 'Recent Guidance News',
    },
    pageId: 101378,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/guidance',
      description: 'Track company guidance and forward-looking statements with our comprehensive guidance calendar.',
    },
    serverSide: {
      columnDef: GuidanceCalendarColumnsDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(OneMonthBefore),
          ...(options.params && options.params),
        };

        return fetchGuidanceData(params, false, 1000);
      },
    },
    title: formatCalendarMetaTitle('Guidance Calendar', oneMonthBeforeDateStroke),
  },
  'insider-trades': {
    calendar: {
      interval: FiveYearsBeforeInterval,
    },
    calendarDate: new Date(),
    component: InsiderTradesCalendar,
    description:
      'The table below is a calendar list view of insider transactions amounts, trade dates and executives completing transactions. Insider trades include the CEO, CFO, board of directors and is updated today.',
    name: 'Insider Trades',
    news: null,
    pageId: 281175, // Need to review
    seo: {
      canonical: 'https://www.benzinga.com/calendars/insider-trades',
      description: 'Track insider buying and selling activity with our comprehensive insider trades calendar.',
    },
    serverSide: {
      columnDef: InsiderTradesCalendarColumnsDef,
      fetchData: (options?: any) => {
        const dateRange = formatIntervalLastOpenMarketDay(fiveYearsBeforeDateStroke, now);
        const params = {
          ...formatIntervalQueryParams(dateRange),
          ...(options.params && options.params),
        };
        return fetchOptionsData(params, 1000);
      },
    },
    title: formatCalendarMetaTitle('Insider Trades', getLastOpenMarketDay(now).date),
  },
  'insider-trades-networth': {
    calendar: {
      interval: FiveYearsBeforeInterval,
    },
    calendarDate: new Date(),
    component: InsiderTradesCalendar,
    description:
      'The table below is a calendar list view of insider transactions amounts, trade dates and executives completing transactions. Insider trades include the CEO, CFO, board of directors and is updated today.',
    name: 'Insider Trades Net Worth',
    news: null,
    pageId: 281175, // Need to review
    serverSide: {
      columnDef: InsiderTradesNetWorthColumnsDef,
      fetchData: (options?: any) => {
        const dateRange = formatIntervalLastOpenMarketDay(fiveYearsBeforeDateStroke, now);
        const params = {
          ...formatIntervalQueryParams(dateRange),
          ...(options.params && options.params),
        };
        return fetchOptionsData(params, 1000);
      },
    },
    title: formatCalendarMetaTitle('Insider Trades', getLastOpenMarketDay(now).date),
  },
  'insider-trades-v2': {
    calendar: {
      interval: FiveYearsBeforeInterval,
    },
    calendarDate: new Date(),
    component: InsiderTradesCalendar,
    description:
      'The table below is a calendar list view of insider transactions amounts, trade dates and executives completing transactions. Insider trades include the CEO, CFO, board of directors and is updated today.',
    name: 'Insider Trades',
    news: null,
    pageId: 281175, // Need to review
    seo: {
      canonical: 'https://www.benzinga.com/calendars/insider-trades',
      description: 'Track insider buying and selling activity with our comprehensive insider trades calendar.',
    },
    serverSide: {
      colorRowByValue: true,
      colorRowField: 'traded_shares',
      columnDef: InsiderTradesCalendarColumnsDefV2,
      fetchData: (options?: any) => {
        const dateRange = formatIntervalLastOpenMarketDay(fiveYearsBeforeDateStroke, now);
        const params = {
          ...formatIntervalQueryParams(dateRange),
          ...(options.params && options.params),
        };
        return fetchOptionsData(params, 1000);
      },
    },
    title: formatCalendarMetaTitle('Insider Trades Net Worth', getLastOpenMarketDay(now).date),
  },
  ipos: {
    calendar: {
      fallbackInterval: TwoMonthsAfterInterval,
      interval: ThirtyDaysAfterInterval,
    },
    component: IPOCalendar,
    name: 'IPO',
    news: {
      params: {
        channels: ['IPOs'],
        pageSize: 10,
      },
      title: 'Recent News',
    },
    pageId: 94564,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/ipos',
      description:
        "An initial public offering (IPO) is a company's 1st entry into the public stock market. Keep track of which compan ies are going public and when",
    },
    serverSide: {
      columnDef: IPOColumnsDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(ThirtyDaysAfterInterval),
          ...(options.params && options.params),
        };

        return fetchIPOData(params, false, 1000);
      },
    },
    title: formatCalendarMetaTitle('IPO Calendar', getLastOpenMarketDay(now).date),
  },
  'm-a': {
    calendar: {
      interval: TwoMonthsBefore,
    },
    component: MACalendar,
    name: 'M&A',
    news: {
      params: {
        channels: ['M&A'],
        pageSize: 10,
      },
      title: 'Recent M&A News',
    },
    pageId: 101385,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/m-a',
      description: 'Stay informed about mergers and acquisitions with our comprehensive M&A calendar.',
    },
    serverSide: {
      columnDef: MACalendarColumnsDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(TwoMonthsBefore),
          ...(options.params && options.params),
        };

        return fetchMAData(params, 1000);
      },
    },
    title: formatCalendarMetaTitle('Merger and Acquisitions', twoMonthsBeforeDateStroke, '(M&A)'),
  },
  'short-interest': {
    calendar: {
      interval: SevenDaysBeforeInterval,
    },
    calendarDate: new Date(),
    component: ShortInterestCalendar,
    description: '',
    name: 'Short Interest',
    news: null,
    pageId: 281175, // Need to review
    seo: {
      canonical: 'https://www.benzinga.com/calendars/short-interest',
      description: 'Track short interest data for stocks with our comprehensive short interest calendar.',
    },
    serverSide: {
      columnDef: ShortInterestCalendarColumnDef,
      fetchData: (options?: any) => {
        const dateRange = formatIntervalLastOpenMarketDay(now.minus({ months: 1 }), now);
        const params = {
          ...formatIntervalQueryParams(dateRange),
          ...(options.params && options.params),
        };
        return fetchOptionsData(params, 1000);
      },
    },
    title: formatCalendarMetaTitle('Short Interest', getLastOpenMarketDay(now).date),
  },
  spac: {
    calendar: {
      interval: ThirtyDaysBeforeInterval,
    },
    component: SpacCalendar,
    name: 'SPAC',
    news: {
      params: {
        channels: ['SPAC'],
        pageSize: 10,
      },
      title: 'Recent SPAC News',
    },
    pageId: 105605,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/spac',
      description:
        'Stay informed about Special Purpose Acquisition Companies (SPACs) with our comprehensive SPAC calendar.',
    },
    serverSide: {
      columnDef: SPACColumnsDefinition,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(ThirtyDaysBeforeInterval),
          ...(options.params && options.params),
        };

        return fetchSPAC(params, false, 1000);
      },
    },
    title: formatCalendarMetaTitle('SPAC Calendar', thirtyDaysBeforeDateStroke),
  },
  'stock-splits': {
    calendar: {
      interval: formatInterval(sevenDaysBeforeDateStroke, now.plus({ days: 60 })),
    },
    component: StockSplitsCalendar,
    name: 'Stock Splits',
    news: {
      params: {
        channels: ['Stock Split'],
        pageSize: 10,
      },
      title: 'Upcoming Stock Split News',
    },
    pageId: 99749,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/stock-splits',
      description:
        'Learn which recent and upcoming company shares will split and when in this regularly updated stocks splits calendar from Benzinga',
    },
    serverSide: {
      columnDef: StockSplitsColumnsDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(formatInterval(sevenDaysBeforeDateStroke, now.plus({ days: 60 }))),
          ...(options.params && options.params),
        };

        return fetchStockSplitsData(params, false, 1000);
      },
    },
    title: formatCalendarMetaTitle('Upcoming Stock Splits', sevenDaysBeforeDateStroke),
  },
  'unusual-options-activity': {
    calendar: {
      interval: SevenDaysBeforeInterval,
    },
    calendarDate: new Date(),
    component: OptionsCalendar,
    description:
      'The table below is a calendar list view of unusual options activity which shows contracts which are trading at abnormal volume levels or price levels. Traders typically use unusual options activity data to identify especially bullish or bearish bets made with high dollar amounts.',
    name: 'Options',
    news: {
      params: {
        channels: ['Options'],
        pageSize: 10,
        topics: ['Unusual Options Activity'],
      },
      title: 'Options Activity',
    },
    pageId: 98763,
    seo: {
      canonical: 'https://www.benzinga.com/calendars/unusual-options-activity',
      description:
        'Unusual options activity shows contracts which are trading at abnormal volume levels or price levels. Traders typically use unusual options activity data to identify especially bullish or bearish bets made with high dollar amounts.',
    },
    serverSide: {
      columnDef: OptionsCalendarColumnsDef,
      fetchData: (options?: any) => {
        const params = {
          ...formatIntervalQueryParams(SevenDaysBeforeInterval),
          ...(options.params && options.params),
        };

        return fetchOptionsData(params, 1000);
      },
    },
    title: formatCalendarMetaTitle('Unusual Options Activity', getLastOpenMarketDay(now).date),
  },
};

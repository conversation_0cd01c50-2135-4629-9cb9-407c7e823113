import { Ticker } from '@benzinga/ticker-ui';

export const TickerLink = ({ symbol }: any): JSX.Element => {
  return (
    <a href={`/quote/${symbol}`} rel="noreferrer" target="_blank">
      {symbol}
    </a>
  );
};

export const TickerWidgetCell = ({
  data,
  rowIndex,
  tickerDescriptionKey,
  tickerLogoVisible,
  tickerSymbolKey,
  value,
}: any): JSX.Element => {
  const symbol = data?.[tickerSymbolKey] ?? value;

  const description = tickerDescriptionKey ? data?.[tickerDescriptionKey] : '';
  const symbols = Array.isArray(symbol) ? symbol : [symbol];

  const TickerWidget = ({ symbol }: any) => {
    if (!symbol) return null;
    return (
      <Ticker
        portalElement={document.querySelector('.calendar-table')}
        symbol={symbol}
        targetElement={<TickerLink symbol={symbol} />}
      />
    );
  };

  const tickers = () => {
    if (!symbols.length) return '-';

    return symbols.map((symbol, index) => {
      return (
        <div key={`${symbol}-${rowIndex}-${index}`}>
          {description && symbol ? (
            <div style={{ display: 'flex' }}>
              {description} ({symbol && <TickerWidget symbol={symbol} />})
            </div>
          ) : description ? (
            description
          ) : (
            symbol && <TickerWidget symbol={symbol} />
          )}

          {data.logo && tickerLogoVisible ? (
            <img alt={symbol} className="bz-ag-table__company-image" loading="lazy" src={data.logo} />
          ) : null}
        </div>
      );
    });
  };

  return <>{tickers()}</>;
};

export const SecFilingLink = ({ data }: any): JSX.Element => {
  return (
    <a href={data.index_url} rel="noreferrer" target="_blank">
      View
    </a>
  );
};

export const InsiderNetWorthLink = ({ data }: any): JSX.Element => {
  const insiderLinks = data.insiders.map((insider: any) => {
    const name = insider.name.replaceAll(' ', '-');
    const netWorthUrl = `https://www.benzinga.com/sec/insider-trades/${insider.cik}/${name}`;
    return (
      <a href={netWorthUrl} key={insider.cik} rel="noreferrer" target="_blank">
        {insider.name}
      </a>
    );
  });
  return insiderLinks;
};

export const InsiderTickerCikLink = ({ data }: any): JSX.Element => {
  const tickerCik = data?.company_cik;
  const netWorthUrl = `https://www.benzinga.com/sec/insider-trades/search?company_cik=${tickerCik}`;
  return (
    <a href={tickerCik ? netWorthUrl : ''} rel="noreferrer" target="_blank">
      {data.company_ticker}
    </a>
  );
};

'use client';
import React, { useEffect, useState } from 'react';
import dynamic from 'next/dynamic';

import styled from '@benzinga/themetron';
import Hooks from '@benzinga/hooks';
import { AuthContainer, PaywallContentType } from '@benzinga/auth-ui';
import { useIsUserPaywalled } from '@benzinga/user-context';
import Loader from './Loader';
import DateFilterBar from './DateFilterBar';
import { IDatePickerDateProps, ILayoutProps } from '../interfaces';
import Tabs from './Tabs';
import ProRecommendations from './ProRecommendations';
import { useFilters } from '@benzinga/filter-ui';
import { ServerSideCalendar } from './ServerSideCalendar';

const BzEdgeCTA = React.lazy(() => import('@benzinga/ads').then(module => ({ default: module.BzEdgeCTA })));

const BasicAdBanner = dynamic(() => import('@benzinga/ads').then(module => module.BasicAdBanner), {
  ssr: true,
});

const StockTable = React.lazy(() =>
  import('./StockTable').then(module => ({
    default: module.StockTable,
  })),
);

const Layout: React.FC<ILayoutProps> = ({
  additionalGridOptions,
  calendar,
  calendarData,
  calendarType,
  columnDefs,
  defaultAdditionalParams = null,
  disableSettingGridApi,
  enableDatePicker = true,
  enableSearch,
  filterBarChildren,
  hasStockTableStyling,
  height,
  hiddenColumns,
  hiddenColumnsWhenGated,
  hideFilters,
  initialDateRange,
  initialTabIndex,
  isGated = false,
  isRouteLoading = false,
  leftSlot,
  noRowsOverlayText,
  onFilterChanged,
  onGridFilterChanged,
  onRowDataChanged,
  onTabChange,
  onTableReady = () => undefined,
  rowHeight,
  searchKey = 'tickers',
  searchMode = 'search',
  showProRecommendations = false,
  tableData,
  tableIndex = 0,
  tableLoading,
  tabs,
  ticker,
  viewportChange,
}) => {
  const [filterParams, setFilterParams] = useFilters<Record<string, any>>(
    {
      date_from: initialDateRange ? initialDateRange.date_from : null,
      date_to: initialDateRange ? initialDateRange.date_to : null,
      ...defaultAdditionalParams,
      [`${searchKey}`]: ticker ?? null,
    },
    {
      hideQueryKeys: [...(enableDatePicker ? [] : ['date_from', 'date_to'])],
    },
  );

  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<any[]>(tableData || []);
  const [search, setSearch] = useState(filterParams[searchKey] || '');
  const mounted = Hooks.useIsMounted();

  const paywall = useIsUserPaywalled(
    isGated ? 'com/read' : '',
    isGated ? 'unlimited-calendars' : '',
    isGated ? 'hard' : 'soft-hard',
  );
  const [isPaywallActive, setIsPaywallActive] = React.useState(paywall?.active);

  useEffect(() => {
    setIsPaywallActive(paywall?.active);
  }, [paywall.active]);

  // mounted, onFilterChanged are excluded here, because of a infinite loading issue
  /* eslint-disable react-hooks/exhaustive-deps */
  useEffect(() => {
    if (mounted && onFilterChanged) {
      onFilterChanged(formatFiltersParams(filterParams));
    }
  }, [filterParams]);

  useEffect(() => {
    if (mounted && onFilterChanged) {
      const params = {
        ...filterParams,
        [`${searchKey}`]: ticker,
      };

      onFilterChanged(formatFiltersParams(params));
    }
  }, [ticker]);
  /* eslint-enable react-hooks/exhaustive-deps */

  useEffect(() => {
    setData(tableData ?? []);
  }, [tableData]);

  useEffect(() => {
    setLoading(tableLoading);
  }, [tableLoading]);

  function getFilterParameter(key: string) {
    return filterParams[key];
  }

  function formatFiltersParams(params: any) {
    const data = {};

    for (const key in params) {
      data[`parameters[${key}]`] = params[key];
    }

    return data;
  }

  function onDateChange(date: IDatePickerDateProps) {
    if (date.interval) {
      const params = {
        ...filterParams,
        date_from: date.start,
        date_to: date.end,
      };

      setFilterParams(params);
    } else {
      setFilterParameter('date', date);
    }
  }

  function isDateChanged() {
    return (
      getFilterParameter('date_from') !== initialDateRange.date_from ||
      getFilterParameter('date_to') !== initialDateRange.date_to
    );
  }

  function setFilterParameter(key: string, value: any) {
    const params = {
      ...filterParams,
    };

    params[`${key}`] = value;
    setFilterParams(params);
  }

  function getParams(tickers: string | any[], dateFrom: string | null | undefined, dateTo: string | null | undefined) {
    return {
      ...filterParams,
      date_from: dateFrom,
      date_to: dateTo,
      [`${searchKey}`]: tickers,
    };
  }

  function handleSearch(value: any) {
    if (searchMode === 'tags') {
      const searchQuery = Array.isArray(value) ? value.join(',') : [];

      if (!getFilterParameter(searchKey)) {
        setFilterParams(getParams(searchQuery, null, null));
      } else {
        setFilterParameter(searchKey, searchQuery);
      }

      // if (searchQuery && window && window.ga) {
      //   window.ga('send', 'event', 'User Interaction', 'Options Ticker Search', searchQuery);
      // }
    } else {
      const dateFrom = initialDateRange ? initialDateRange.date_from : null;
      const dateTo = initialDateRange ? initialDateRange.date_to : null;
      const tickers = ticker || '';

      if (!getFilterParameter(searchKey)) {
        const val = value.trim().toUpperCase();
        if (val === '') {
          setFilterParams(getParams(tickers, dateFrom, dateTo));
        } else {
          setFilterParams(getParams(val, null, null));
        }
      } else {
        const val = value.trim().toUpperCase();
        if (val === '') {
          setFilterParams(getParams(tickers, dateFrom, dateTo));
        } else {
          setFilterParameter(searchKey, val);
        }
      }

      setSearch(value);
      // if (value && window && window.ga) {
      //   window.ga('send', 'event', 'User Interaction', 'Options Ticker Search', value);
      // }
    }
  }

  const shouldRenderClientSideCalendar =
    calendarType === 'client_side' ||
    (calendarType !== 'server_side' && !calendarData?.serverSide) ||
    //!isPaywallActive ||
    (calendarType !== 'server_side' && mounted);

  const ServerSideCalendarComponent: React.FC<{ sliceData?: boolean }> = ({ sliceData }) => {
    if (!calendarData) return null;
    return (
      <div className="server-side-calendar-container">
        <ServerSideCalendar
          calendar={calendar}
          calendarData={calendarData}
          calendarDataSet={sliceData ? data.slice(0, 30) : data}
          hasStockTableStyling={hasStockTableStyling}
          height={height}
          hiddenColumns={hiddenColumns}
          hiddenColumnsWhenGated={hiddenColumnsWhenGated}
          isGated={isPaywallActive}
          noDataText={noRowsOverlayText}
          showAd={false}
          ticker={ticker}
        />
      </div>
    );
  };

  const EdgeAd = () => {
    const isTargeted = [
      'unusual-options-activity',
      'earnings',
      'analyst-ratings',
      'insider-trades',
      'analyst-predictions',
    ].includes(calendar ?? '');

    if (isTargeted && calendar) {
      return <BzEdgeCTA type={calendar} />;
    }
    return (
      <BasicAdBanner
        buttonText="Sign up for Benzinga Edge"
        contentAlign="center"
        heading="CLICK HERE to join Benzinga Edge"
        subheading={`Unlock all calendars and get access to the best trading ideas, research, and analysis.`}
        textAlign="center"
        url={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?t=be8be9we4gewe1be11&utm_source=${calendar}`}
      />
    );
  };

  const getCalendarPaywallCopy = () => {
    if (
      ['unusual-options-activity', 'earnings', 'analyst-ratings', 'insider-trades', 'analyst-predictions'].includes(
        calendar ?? '',
      )
    ) {
      return calendar as PaywallContentType;
    }
    return undefined;
  };

  return (
    <CalendarLayoutWrapper className="calendar-table">
      {!hideFilters && (
        <div className="ratings-filters">
          {tabs && onTabChange ? (
            <>
              <Loader loading={isRouteLoading} />
              <Tabs disabled={isRouteLoading} initialTabIndex={initialTabIndex} onChange={onTabChange} tabs={tabs} />
            </>
          ) : (
            leftSlot
          )}

          {filterBarChildren}

          <DateFilterBar
            dateRange={{
              date_from: getFilterParameter('date_from'),
              date_to: getFilterParameter('date_to'),
            }}
            enableDatePicker={enableDatePicker}
            hideSearch={!enableSearch || !!ticker}
            onDateChange={onDateChange}
            onSearch={handleSearch}
            search={search}
            tagsMode={searchMode === 'tags'}
          />
        </div>
      )}
      <React.Suspense>{!shouldRenderClientSideCalendar && <ServerSideCalendarComponent />}</React.Suspense>
      {shouldRenderClientSideCalendar && (
        <>
          <Loader loading={loading} />
          <React.Suspense fallback={<ServerSideCalendarComponent sliceData={true} />}>
            <StockTable
              additionalGridOptions={additionalGridOptions}
              calendarName={calendar}
              columnDefs={columnDefs}
              data={data}
              disableSettingGridApi={disableSettingGridApi}
              enableSearch={!!enableSearch}
              externalFilterTriggered={isDateChanged()}
              height="60vh"
              hiddenColumns={hiddenColumns}
              hiddenColumnsWhenGated={hiddenColumnsWhenGated}
              isGated={isPaywallActive}
              noRowsOverlayText={noRowsOverlayText}
              onFilterChanged={onGridFilterChanged}
              onReady={onTableReady}
              onRowDataChanged={onRowDataChanged}
              onSearch={handleSearch}
              rowHeight={rowHeight}
              search={search}
              setSearch={setSearch}
              showAd={false}
              theme="dark-blue"
              ticker={ticker}
              viewportChange={viewportChange}
            />
          </React.Suspense>
        </>
      )}
      {showProRecommendations && <ProRecommendations rowData={data} />}
      {isPaywallActive && (
        <div className="absolute top-1/3 w-full">
          <EdgeAd />
        </div>
      )}
      {tableIndex === 0 && isPaywallActive && (
        <AuthContainer
          authMode="register"
          contentType={getCalendarPaywallCopy()}
          iterationStyle={paywall?.paywallStyle}
          placement={'calendar - ' + calendarData?.title}
          preventRedirect={true}
          setShowPaywall={setIsPaywallActive}
        />
      )}
    </CalendarLayoutWrapper>
  );
};

export default Layout;

const CalendarLayoutWrapper = styled.div`
  position: relative;

  .status-panel-container {
    margin-bottom: 4px;
    top: 4px;
    .clear-filters {
      text-decoration: underline;
      color: #2ca2d1;
      cursor: pointer;
    }
  }

  .bz-ag-table {
    margin-top: 0;
    margin-bottom: 10px;

    .ag-react-container {
      width: 100%;
    }

    a {
      color: #2ca2d1;
    }

    &__company-image {
      max-height: 18px;
      margin-left: 0.25rem;
      float: right;
    }

    &__analyst-name {
      overflow: hidden;
      white-space: nowrap;
      width: 100%;
    }

    &__analyst-smart-score {
      color: white;
      background: ${({ theme }) => theme.colorPalette.blue600};
      padding: 2px 4px;
      width: 35px;
      max-width: 35px;
      line-height: 20px;
      height: 20px;
      max-height: 20px;
      text-align: center;
      border-radius: ${({ theme }) => theme.borderRadius.full};
      margin-left: 0.1rem;
      float: right;
      display: flex;
      align-items: center;
      justify-content: center;

      &.green {
        background: ${({ theme }) => theme.colorPalette.green500};
      }

      &.yellow {
        background: ${({ theme }) => theme.colorPalette.yellow400};
      }

      &.orange {
        background: ${({ theme }) => theme.colorPalette.orange400};
      }

      &.red {
        background: ${({ theme }) => theme.colorPalette.red600};
      }
    }

    .country-cell {
      & > span {
        display: flex;
        align-items: center;
      }
    }
  }

  div.company-name-cell {
    width: 100%;
    display: inline-flex;
    justify-content: space-between;
    line-height: 19px;

    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
  }

  .server-side-calendar-container {
    display: flex;
    width: 100%;
    overflow-x: auto;
  }

  .analyst-name-and-smart-score-cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .ratings-filters {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-end;
    flex-wrap: wrap;
    width: 100%;
    margin-bottom: 1rem;
    position: relative;
    margin-bottom: 1rem;

    h1 {
      flex-shrink: 0;
      margin-bottom: 10px;
    }

    .dates-filter {
      .range-date-picker {
        &__picker-field {
          font-weight: 500;
        }
      }
      @media screen and (max-width: 500px) {
        justify-content: space-between;
        flex-wrap: nowrap;

        .range-date-picker {
          padding-left: 0;

          &__field-wrapper {
            width: 153px;
          }

          &__picker-field {
            font-size: 10px;
          }
        }

        .bz3-search-bar-wrapper {
          width: auto;
          margin-left: 5px;
        }
      }
    }
  }
`;

'use client';
import React, { useEffect, useCallback, useState, useMemo } from 'react';
import styled from '@benzinga/themetron';
import dynamic from 'next/dynamic';
import { AgGridReact } from '@ag-grid-community/react';
import { ModuleRegistry, IsFullWidthRowParams } from '@ag-grid-community/core';
// import { StatusBarModule } from '@ag-grid-enterprise/status-bar';
// import { MenuModule } from '@ag-grid-enterprise/menu';
// import { ClipboardModule } from '@ag-grid-enterprise/clipboard';
import { ClientSideRowModelModule } from '@ag-grid-community/client-side-row-model';
import {
  GridOptions,
  GridReadyEvent,
  ColDef,
  FirstDataRenderedEvent,
  ViewportChangedEvent,
  SizeColumnsToContentStrategy,
  SizeColumnsToFitGridStrategy,
  SizeColumnsToFitProvidedWidthStrategy,
} from '@ag-grid-community/core';
import { IStockTableProps } from '../interfaces';
import QuarterPeriodCompareFilter from '../filters/QuarterPeriodCompareFilter';
import ColumnPercentageFilter from '../filters/PercentCompareFilter';
import ColumnPriceCompareFilter from '../filters/PriceCompareFilter';
// import Alert from './Alert';
import ClickableStatusBarComponent from './ClickableStatusBarComponent';
import { InsiderNetWorthLink, SecFilingLink, TickerWidgetCell } from './FrameworkComponents';

import InsiderTooltipCellRenderer from '../../InsiderTradesCalendar/InsiderTooltipCell';
import NoRowsOverlay from '../../NoRowsOverlay';

import { ErrorBoundary } from '@benzinga/core-ui';
import InsiderTooltip from './InsiderTooltip';

const BasicAdBanner = dynamic(() => import('@benzinga/ads').then(module => module.BasicAdBanner), {
  ssr: true,
});

ModuleRegistry.registerModules([ClientSideRowModelModule]);
//ModuleRegistry.registerModules([ClipboardModule, StatusBarModule, MenuModule]);

export const StockTable: React.FC<IStockTableProps> = ({
  additionalGridOptions,
  calendarName,
  columnDefs = [],
  data: initialData,
  //disableSettingGridApi,
  enableSearch,
  externalFilterTriggered,
  height = '570px',
  hiddenColumns,
  hiddenColumnsWhenGated,
  isGated,
  noRowsOverlayText,
  onFilterChanged,
  onReady,
  onRowDataChanged,
  onSearch,
  rowHeight = 25,
  search,
  setSearch,
  showAd,
  theme,
  ticker,
  viewportChange,
}) => {
  const gridRef = React.useRef<AgGridReact<any>>(null);
  const [rowData, setRowData] = React.useState<any>(initialData || []);
  const [gridReady, setGridReady] = React.useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [externalFilterUsed, setExternalFilterUsed] = useState(false);

  const [styles, setStyles] = React.useState({
    height,
    maxHeight: height,
  });

  const columns = ticker
    ? columnDefs.map((c: ColDef) => {
        if (ticker) {
          if (c.field === 'ticker' || c.cellRendererParams?.hideOnInitialTicker) {
            return { ...c, hide: true, suppressMenu: true };
          }
        }
        return c;
      })
    : columnDefs.map((c: ColDef) => {
        if (isGated && hiddenColumnsWhenGated?.has(c.field as string)) {
          return { ...c, hide: true, suppressMenu: true };
        }
        return c;
      });

  const gridOptions: GridOptions = {
    defaultColDef: {
      cellStyle: { fontSize: '12px' },
      filter: false,
      menuTabs: ['filterMenuTab'],
      resizable: true,
      sortable: true,
    },
    getRowClass: params => {
      if (isGated && params?.rowIndex && params?.rowIndex > 4) {
        return 'blur-md';
      } else {
        return '';
      }
    },
    getRowHeight: params => (params.node.data.isProBanner ? 140 : rowHeight),
    headerHeight: 36,
    isFullWidthRow: (params: IsFullWidthRowParams) => params.rowNode.data.isProBanner,
    statusBar: {
      statusPanels: [
        {
          align: 'center',
          key: 'statusBarCompKey',
          statusPanel: 'statusBarComponent',
          statusPanelParams: {
            onSearch,
            setSearch,
          },
        },
      ],
    },
    suppressMenuHide: true,
    tooltipShowDelay: 0,
    ...additionalGridOptions,
  };

  const initiate = useCallback(() => {
    setSearchQuery(search);
    setExternalFilterUsed(!!externalFilterTriggered);
    gridRef.current?.api.onFilterChanged();
  }, [search, externalFilterTriggered]);

  useEffect(() => {
    if (gridReady && gridRef.current?.api) {
      initiate();
    }
  }, [gridReady, initiate]);

  useEffect(() => {
    if (gridReady && gridRef.current?.api) {
      if (ticker) {
        gridRef.current.api?.setColumnsVisible(['ticker'], false);
      } else {
        gridRef.current.api?.setColumnsVisible(['ticker'], true);
      }
      if (Array.isArray(hiddenColumns) && hiddenColumns.length > 0) {
        //gridApi.setColumnsVisible(hiddenColumns, false);
        //gridApi.autoSizeAllColumns();
        gridRef.current.api?.setColumnsVisible(hiddenColumns, false);
        //gridRef.current.api?.autoSizeAllColumns();
      }
    }
  }, [ticker, hiddenColumns, gridReady]);

  useEffect(() => {
    if (gridReady) {
      let modifiedRowData: any = [];

      if (initialData?.length) {
        modifiedRowData = [...initialData];

        if (showAd) {
          const proBannerRow = { isProBanner: true };
          if (modifiedRowData.length < 5) {
            modifiedRowData.push(proBannerRow);
          } else {
            modifiedRowData.splice(4, 0, proBannerRow);
          }
        }
      }
      setRowData(modifiedRowData);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [initialData, initialData?.length, showAd, gridReady]);

  const calculateGridHeight = useCallback((): void => {
    if (!rowData || rowData.length === 0) {
      setStyles(prevState => ({
        ...prevState,
        height: '200px',
      }));
    } else if (rowData.length <= 20) {
      setStyles(prevState => ({
        ...prevState,
        height: `${rowHeight * rowData.length + (showAd ? 115 : 0) + 40}px`,
      }));
    } else {
      setStyles(prevState => ({
        ...prevState,
        height: `${rowHeight * 20 + (showAd ? 115 : 0) + 40}px`,
      }));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [rowData, showAd]);

  function onGridReady(params: GridReadyEvent): void {
    setGridReady(true);
    if (gridRef?.current?.api) {
      gridRef.current.api.sizeColumnsToFit();
    }
    //params.api.sizeColumnsToFit();
    // Hack to fix results not displaying on FDA Calendar when changing dates with DateFilter
    // if (!disableSettingGridApi) {
    //   setGridApi(params.api);
    //   setGridColumnApi(params.columnApi);
    // }
    onReady(params);
  }

  function isExternalFilterPresent(): boolean {
    return (!!searchQuery || externalFilterUsed) && enableSearch;
  }

  const doesExternalFilterPass = (node: any): boolean => {
    // eslint-disable-next-line @typescript-eslint/no-shadow
    const search = searchQuery.toLowerCase();
    const ticker =
      node.data.ticker ||
      node.data.symbol ||
      node.data.acquirer_ticker ||
      node.data.target_ticker ||
      node.data.company_ticker;

    if (ticker) {
      return ticker.toLowerCase().includes(search);
    } else if (node.data.companies && node.data.companies.length > 0) {
      const tickers: any[] = [];
      node.data.companies.forEach((c: any) => {
        if (c.securities && c.securities.length !== 0) {
          c.securities.forEach((s: any) => {
            tickers.push(s.symbol.toLowerCase());
          });
        }
      });
      return tickers.includes(search);
    }

    return false;
  };

  function viewportChanged(value: ViewportChangedEvent): void {
    if (viewportChange) {
      viewportChange(value);
    }
  }

  function rowDataChanged(payload: any): void {
    calculateGridHeight();
    if (onRowDataChanged) {
      onRowDataChanged(payload);
      updateColumnsSize(payload);
    }
  }

  function updateColumnsSize(payload: any) {
    if (payload?.columnApi && payload?.api) {
      if (window.outerWidth < 1200) {
        payload.columnApi.sizeColumnsToFit(1200);
      } else {
        payload.api.sizeColumnsToFit();
        payload.columnApi.sizeColumnsToFit();
      }
    }
  }

  function onFirstDataRendered(params: FirstDataRenderedEvent): void {
    updateColumnsSize(params);
  }

  function getTableClassName(): string {
    let className = `bz-ag-table ag-theme-alpine`;
    if (theme) {
      className += ` bz-ag-table--${theme}`;
    }
    if (isGated) {
      className += ' blur-rows';
    }
    return className;
  }

  const getContextMenuItems = () => {
    return ['copy', 'copyWithHeaders'];
  };

  const rowAd = () => (
    <BasicAdBanner
      buttonText="Sign up for Benzinga Edge"
      contentAlign="center"
      heading="CLICK HERE to join Benzinga Edge"
      subheading={
        calendarName === 'analyst-ratings'
          ? "Unlock all major upgrades, downgrades, from the market's most accurate analysts."
          : 'Earnings calendar plus other high-impact tools and content.'
      }
      textAlign="center"
      url={`https://www.benzinga.com/premium/ideas/benzinga-edge-checkout/?t=be8be9we4gewe1be11&utm_source=${calendarName}`}
    />
  );

  const components = useMemo(
    () => ({
      basicAdBanner: rowAd,
      insiderNetworthLink: InsiderNetWorthLink,
      insiderTooltipCell: InsiderTooltipCellRenderer,
      noRowsOverlay: NoRowsOverlay,
      percentFilter: ColumnPercentageFilter,
      priceFilter: ColumnPriceCompareFilter,
      quarterFilter: QuarterPeriodCompareFilter,
      secFilingLink: SecFilingLink,
      statusBarComponent: ClickableStatusBarComponent,
      tickerWidgetCell: TickerWidgetCell,
    }),
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [],
  );

  const autoSizeStrategy = useMemo<
    SizeColumnsToFitGridStrategy | SizeColumnsToFitProvidedWidthStrategy | SizeColumnsToContentStrategy
  >(() => {
    return {
      type: 'fitGridWidth',
    };
  }, []);

  return (
    <CalendarWrapper>
      <div className={getTableClassName()} style={styles}>
        <ErrorBoundary name="StockTable">
          <AgGridReact
            autoSizeStrategy={autoSizeStrategy}
            columnDefs={columns}
            components={components}
            doesExternalFilterPass={doesExternalFilterPass}
            fullWidthCellRenderer="basicAdBanner"
            getContextMenuItems={getContextMenuItems}
            gridOptions={gridOptions}
            isExternalFilterPresent={isExternalFilterPresent}
            //isFullWidthRow={isFullWidthRow}
            noRowsOverlayComponent={'noRowsOverlay'}
            noRowsOverlayComponentParams={{
              calendarName: calendarName,
              initialTickers: ticker,
              text: noRowsOverlayText,
            }}
            onFilterChanged={onFilterChanged}
            onFirstDataRendered={onFirstDataRendered}
            onGridReady={onGridReady}
            onRowDataUpdated={rowDataChanged}
            onViewportChanged={viewportChanged}
            reactiveCustomComponents={true}
            ref={gridRef}
            rowData={rowData}
          />
        </ErrorBoundary>
      </div>
      <InsiderTooltip />
      <div className="under-calendar-text">
        Data brought to you by{' '}
        <a
          href="https://www.benzinga.com/apis/data/?utm_source=benzinga.com&amp;utm_medium=web&amp;utm_campaign=internal"
          rel="noreferrer"
          target="_blank"
        >
          Benzinga APIs
        </a>
      </div>
    </CalendarWrapper>
  );
};

export default StockTable;

export const CalendarWrapper = styled.div`
  .bz-ag-table .ag-ltr .ag-cell {
    border-right: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
    align-items: center;
    display: inline-flex;
    justify-content: space-between;
  }

  .bz-ag-table.blur-rows {
    .ag-row:nth-child(n + 5) {
      filter: blur(4px);
      pointer-events: none;
    }
  }

  .ag-cell .ag-cell-value {
    span {
      width: 100%;
    }
  }

  .ag-root-wrapper-body {
    height: 100%;
  }

  .bz-ag-table {
    background-color: #fff;
    border-left: solid 1px #ddd;
    border-right: solid 1px #ddd;
    border-bottom: solid 1px #ddd;
    border-radius: 0px;
    margin: 0.5rem 0;
    //min-height: 400px;

    .ag-header-cell-text {
      font-size: 12px;
      font-family: sans-serif;
      font-weight: 700;
    }

    &__company-image {
      max-height: 18px;
      margin-left: 4px;
      float: right;
    }

    .ag-root-wrapper {
      border: none;
      background: #fff;
    }

    .ag-header {
      background: none;
    }

    .ag-header-cell-resize {
      display: none;
    }

    .ag-row {
      height: 28px;
      background: #fff;

      &:hover {
        background: rgba(64, 192, 243, 0.1);

        .gain-cell {
          background: #cef1ce;
        }

        .lose-cell {
          background: #fdcece;
        }
      }
    }

    &__logo {
      height: 17px;
      max-width: none;
      margin-left: 10px;
    }

    .ag-body-viewport {
      &::-webkit-scrollbar {
        width: 16px;
      }

      /* Track */
      &::-webkit-scrollbar-track {
        background-color: #fff;
      }

      /* Handle */
      &::-webkit-scrollbar-thumb {
        background-color: #babac0;
        border-radius: 16px;
        border: 5px solid #fff;
        min-height: 50px;
      }

      /* Handle on hover */
      &::-webkit-scrollbar-thumb:hover {
        background-color: #a0a0a5;
      }
    }

    .ag-row-odd {
      background: #efefef;

      /* .premarket-intens-indicator {
        &:before {
          border-color: rgb(238, 238, 238) transparent transparent transparent;
        }
      } */
    }

    .ag-header-cell {
      font-size: 13px;
      line-height: 21px;
      font-weight: bold;
      color: #000;
      padding: 4px 10px;

      &:focus {
        outline: none;
      }
    }

    .ag-header-row {
      height: 21px;
    }

    .ag-row {
      font-size: 10px;
      line-height: 14px;
      border: 0;
    }

    .ag-header-icon.ag-header-label-icon.ag-filter-icon {
      display: none;
    }

    .ag-header-cell-menu-button {
      opacity: 1;
    }

    .ag-icon-menu,
    .ag-icon-filter {
      &:before {
        content: '';
        background: url('/next-assets/images/icons/table/filter.svg') no-repeat;
        width: 10px;
        height: 10px;
        display: block;
      }
    }

    .ag-header-cell-filtered {
      .ag-header-icon .ag-icon-menu {
        &:before {
          background: url('/next-assets/images/icons/table/filter-filled.svg') no-repeat;
        }
      }
    }

    .ag-cell {
      border: none;
      border-right: 1px solid #aaa;
      border-bottom: 1px solid #aaa;
      padding: 4px 10px;
      line-height: 16px;
      color: #333;
      font-weight: 700;
      font-family: sans-serif;
      height: 100%;

      &:focus {
        outline: none;
      }

      &.ticker-cell {
        font-weight: bold;
        font-family: Manrope, Manrope-fallback, sans-serif;
      }

      &.insider-cell {
        overflow: visible;
      }

      &.wrap-cell {
        font-weight: bold;
        white-space: normal;
        overflow-wrap: break-word;
      }

      &.gain-cell {
        color: #009900;
        background: #f2faf2;
      }

      &.lose-cell {
        color: #cc0000;
        background: #f9e5e5;
      }

      &.importance-cell {
        padding: 4px 4px;
      }

      &:last-child {
        border-right: 0;
      }
    }

    &--dark-blue {
      .ag-header-icon {
        color: white;
      }

      .ag-header-cell {
        border-left: 1px solid #aaa;
        &:first-child {
          border-left: none;
        }
      }

      .ag-header-viewport {
        background: #072232;
      }

      .ag-header-container {
        background: #072232;
        border-bottom: 3px solid #2ca2d1;
      }

      .ag-header-cell-text {
        font-size: 12px;
        color: #fff;
        font-family: sans-serif;
        font-weight: 700;
      }

      .ag-icon-menu,
      .ag-icon-filter {
        &:before {
          content: '';
          background: url('/next-assets/images/icons/table/filter-filled-white.svg') no-repeat;
        }
      }
    }
  }
  .under-calendar-text {
    font-size: ${({ theme }) => theme.fontSize.base};
  }
`;

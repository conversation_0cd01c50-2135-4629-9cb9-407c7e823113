import { ColDef, ColGroupDef, ViewportChangedEvent, IStatusPanelParams, GridOptions } from '@ag-grid-community/core';
import { TabInterface } from '../components/Tabs';
import { NewsFeedQueryParams } from '@benzinga/basic-news-manager';
import { AnalystData, CalendarDataParams } from '@benzinga/calendar-manager';
export interface IDateRange {
  date_from?: string;
  date_to?: string;
}

export interface ServerSideFetchOptions {
  tab?: string;
  params?: CalendarDataParams;
}
export interface CalendarDataI {
  analystData?: AnalystData;
  component: React.FC;
  injectTickersLogos?: boolean;
  title: string;
  name?: string;
  calendar?: {
    interval?: IDateRange;
    fallbackInterval?: IDateRange;
  };
  separateTitle?: boolean;
  description?: string;
  calendarDate?: Date;
  news: {
    title: string;
    params: NewsFeedQueryParams;
  } | null;
  pageId: number;
  serverSide?: {
    fetchData: (options?: ServerSideFetchOptions) => Promise<any[]>;
    columnDef: any;
    colorRowByValue?: boolean;
    colorRowField?: string;
  };
  routes?: { [key: string]: CalendarDataI };
  seo?: {
    title?: string;
    description?: string;
    canonical?: string;
  };
  campaignifyUTM?: string;
  campaignifyVariant?: string;
  showProCTA?: boolean;
}

export interface ICalendarProps {
  additionalFetchParams?: CalendarDataParams;
  calendar?: string;
  calendarData?: CalendarDataI;
  calendarType?: 'server_side' | 'client_side';
  datePicker?: boolean;
  height?: number;
  hiddenColumns?: string[];
  hasStockTableStyling?: boolean;
  initialTab?: string | number;
  initialData?: any;
  initialDateRange?: IDateRange | null;
  initialTicker?: string;
  leftSlot?: any;
  hideFilters?: boolean;
  onReady?: () => void;
  onTabChange?: (tabKey: string | boolean) => void;
  onDataLoad?: (data: any) => void;
  isRouteLoading?: boolean;
  noRowsOverlayText?: string;
}

type CalendarTabSlugs = 'analyst-ratings' | 'guidance' | 'dividends' | 'earnings' | 'short-interest' | 'insider-trades';

export interface ILayoutProps extends Omit<ICalendarProps, 'onTabChange'> {
  additionalGridOptions?: GridOptions;
  columnDefs?: (ColDef | ColGroupDef)[];
  defaultAdditionalParams?: any;
  disableSettingGridApi?: boolean;
  enableDatePicker?: boolean;

  enableSearch: boolean;
  filterBarChildren?: any;
  hiddenColumnsWhenGated?: Set<string>;
  leftSlot?: any;
  initialTabIndex?: number;
  initialDateRange: IDateRange;
  isGated?: boolean;
  onFilterChanged?: (params: any) => void;
  onGridFilterChanged?: (params: any) => void;
  onRowDataChanged?: (params: any) => void;

  onTabChange?: (index: number) => void;
  onTableReady?: (params: any) => void;
  hasStockTableStyling?: boolean;
  hideFilters?: boolean;
  rowHeight?: number;
  searchKey?: string;
  searchMode?: string;
  showProRecommendations?: boolean;
  tableData?: any[];
  tableLoading: boolean;
  tabs?: TabInterface[];
  ticker?: string;
  viewportChange?: (event: ViewportChangedEvent) => void;
  premiumProductLink?: string[] | any;
  tableIndex?: number; // Added tableIndex prop to support multiple tables that are paywalled
}

export interface IStockTableProps {
  additionalGridOptions?: GridOptions;
  calendarName?: string;
  columnDefs?: (ColDef | ColGroupDef)[];

  data?: any[];
  disableSettingGridApi?: boolean;

  enableSearch: boolean;

  externalFilterTriggered?: boolean;
  height?: string;
  hiddenColumns?: string[];
  hiddenColumnsWhenGated?: Set<string>;
  onFilterChanged?: (params: any) => void;

  onReady: (params: any) => void;
  onRowDataChanged?: (params: any) => void;
  onSearch?: (value: string) => void;
  rowHeight?: number;
  search: string;
  setSearch?: (value: string) => void;
  isGated?: boolean;
  showAd?: boolean;
  theme?: string;
  ticker?: string;
  noRowsOverlayText?: string;
  viewportChange?: (event: ViewportChangedEvent) => void;
}

export interface IDateFilterBarProps {
  children?: any;

  dateRange: IDateRange;
  enableDatePicker: boolean;

  hideSearch: boolean;
  onDateChange: (date: any) => void;
  onSearch: (value: string | string[]) => void;
  search: string;
  tagsMode: boolean;
}

export interface IExtendedDatePickerProps {
  dateFormat?: string;

  end: undefined | string | null | Date;
  onChangeDate: (date: IDatePickerDateProps) => void;
  range?: DateRating;
  start: undefined | string | null | Date;
}

export interface ISearchFieldProps {
  onSearch: (value: string | string[]) => void;

  placeholder?: string;
  search: string;
  tagsMode?: boolean;
}

export interface IClickableStatusBarComponentProps extends IStatusPanelParams {
  onSearch: (value: string) => void;
  setSearch: (value: string) => void;
}

export interface IDatePickerDateProps {
  end: any;
  interval?: boolean;
  start: any;
}

export type DateRating =
  | 'this-week'
  | 'this-month'
  | 'last-month'
  | 'rolling-7-days'
  | 'rolling-30-days'
  | 'week-to-date'
  | 'today'
  | 'clear'
  | null;

export interface IImportAllParams {
  (id: string): any;
  <T>(id: string): T;
  keys: () => string[];
  id: string;
  resolve: (id: string) => string;
}

export type CalendarType =
  | 'analyst-ratings'
  | 'conference-calls'
  | 'dividends'
  | 'economic'
  | 'earnings'
  | 'guidance'
  | 'insider-trades'
  | 'insider-trades-v2'
  | 'insider-trades-networth'
  | 'ipos'
  | 'short-interest'
  | 'spac'
  | 'stock-splits'
  | 'unusual-options-activity'
  | 'm-a'
  | 'fda'
  // | 'retail-sales'
  | 'analyst-predictions';

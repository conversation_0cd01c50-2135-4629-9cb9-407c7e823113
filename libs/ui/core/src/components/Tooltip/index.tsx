'use client';

import React from 'react';
import styled from '@benzinga/themetron';
import Portal from '../../utils/Portal';
import TooltipPopover from './TooltipPopover';
import Hooks from '@benzinga/hooks';
import { debounce } from '@benzinga/utils';

export interface TooltipProps {
  content?: React.ReactNode;
  delay?: number;
  width?: number;
  visible?: boolean;
  trigger?: 'hover' | 'click';
  size?: 'sm' | 'md' | 'lg';
  space?: number;
  position?: 'top' | 'bottom';
  isContentHtml?: boolean;
}

interface TooltipStyles {
  tooltipStyle: React.CSSProperties;
}

export const Tooltip: React.FC<React.PropsWithChildren<TooltipProps>> = React.memo(
  ({
    children,
    content,
    delay = 0,
    isContentHtml = false,
    position,
    size = 'md',
    space = 10,
    trigger = 'hover',
    visible = false,
    width = 100,
  }) => {
    const [isVisible, setIsVisible] = React.useState(visible);
    const [style, setStyle] = React.useState<React.CSSProperties | undefined>({});
    const [tooltipArrowStyle, setTooltipArrowStyle] = React.useState<{ [key: string]: string } | undefined>({});
    const containerRef = React.useRef<HTMLDivElement>(null);
    const tooltipPopoverRef = React.useRef<HTMLDivElement>(null);

    React.useEffect(() => {
      if (typeof visible === 'boolean' && trigger === 'click') {
        setIsVisible(visible);
      }
    }, [trigger, visible]);

    Hooks.useClickOutside(containerRef, (event?: MouseEvent) => {
      if (!event) {
        setIsVisible(false);
        return;
      }
      if (
        trigger === 'click' &&
        !visible &&
        tooltipPopoverRef?.current &&
        !tooltipPopoverRef.current.contains(event.target as Node)
      ) {
        setIsVisible(false);
      }
    });

    const calculateTooltipPosition = React.useCallback(
      (styles: TooltipStyles) => {
        const dimensions = containerRef?.current?.getBoundingClientRect();
        if (!dimensions) return {};

        const newTooltipStyle: React.CSSProperties = {
          width,
        };

        const newTooltipArrowStyle: { [key: string]: string } = {};

        const hasRoomToDisplayOnTop = dimensions.top > Number(styles?.tooltipStyle?.height) + space;
        const offTheScreenMarginLeft = dimensions.left + dimensions.width / 2 - width / 2;
        const offTheScreenMarginRight = document.body.clientWidth - width - space;
        const tooltipArrowPosition = (dimensions.left + dimensions.right) / 2;
        newTooltipStyle.left = offTheScreenMarginLeft;
        newTooltipStyle.left = Math.max(space, newTooltipStyle.left);
        newTooltipStyle.left = Math.min(newTooltipStyle.left, offTheScreenMarginRight);

        if (Math.sign(offTheScreenMarginLeft) === -1) {
          newTooltipArrowStyle.left = `${tooltipArrowPosition - space}px`;
        } else if (offTheScreenMarginLeft > offTheScreenMarginRight) {
          newTooltipArrowStyle.left = `${tooltipArrowPosition - space}px`;
        }

        if (position === 'top' || (position !== 'bottom' && hasRoomToDisplayOnTop)) {
          newTooltipStyle.top = styles?.tooltipStyle?.top;
          newTooltipStyle.transform = 'unset';
        } else {
          newTooltipStyle.top = dimensions.top + dimensions.height + space;
          newTooltipStyle.transform = 'unset';
          newTooltipArrowStyle.top = '-10px';
          newTooltipArrowStyle.transform = 'rotateX(180deg)';
        }

        return { tooltip: newTooltipStyle, tooltipArrow: newTooltipArrowStyle };
      },
      [position, space, width],
    );

    const getTooltipPopoverStyles = React.useCallback(() => {
      const containerRect = containerRef?.current?.getBoundingClientRect();
      const tooltipPopoverHeight = Number(tooltipPopoverRef?.current?.clientHeight);
      const containerTopPosition = Number(containerRect?.top);
      const newTooltipPopoverTop = containerTopPosition - tooltipPopoverHeight - space + window.scrollY;
      return { height: tooltipPopoverHeight, top: newTooltipPopoverTop };
    }, [space]);

    const updateStyles = React.useCallback(
      (styles: TooltipStyles) => {
        const newTooltipStyle = calculateTooltipPosition(styles);
        setStyle(newTooltipStyle.tooltip);
        setTooltipArrowStyle(newTooltipStyle.tooltipArrow);
      },
      [calculateTooltipPosition],
    );

    const handleUpdateTooltipCoords = React.useCallback(() => {
      const tooltipPopoverTop = getTooltipPopoverStyles();
      updateStyles({ tooltipStyle: tooltipPopoverTop });
    }, [getTooltipPopoverStyles, updateStyles]);

    React.useEffect(() => {
      if (isVisible) handleUpdateTooltipCoords();
    }, [handleUpdateTooltipCoords, isVisible]);

    const showTooltip = React.useCallback(() => {
      setIsVisible(true);
    }, []);

    const hideTooltip = React.useCallback(() => {
      setIsVisible(false);
    }, []);

    const debouncedHideTooltip = delay ? debounce(hideTooltip, delay) : hideTooltip;

    const handleToggleTooltipOnClick = React.useCallback(() => {
      if (isVisible) hideTooltip();
      else showTooltip();
    }, [hideTooltip, isVisible, showTooltip]);

    return (
      <Container
        className={`tooltip-container ${trigger}`}
        id="tooltip-container"
        onClick={trigger === 'hover' ? undefined : handleToggleTooltipOnClick}
        onMouseOut={trigger === 'click' ? undefined : debouncedHideTooltip}
        onMouseOver={trigger === 'click' ? undefined : showTooltip}
        ref={containerRef}
      >
        {children}
        {isVisible && (
          <Portal>
            {content && (
              <TooltipPopover
                ref={tooltipPopoverRef}
                size={size}
                style={style}
                tooltipArrowStyle={tooltipArrowStyle}
                updateTooltipCoords={handleUpdateTooltipCoords}
              >
                {isContentHtml ? <div dangerouslySetInnerHTML={{ __html: String(content) }} /> : content}
              </TooltipPopover>
            )}
          </Portal>
        )}
      </Container>
    );
  },
);

export default Tooltip;

const Container = styled.div`
  &.tooltip-container {
    display: inline-flex;
    position: relative;
    align-items: center;

    &.click {
      cursor: pointer;
    }
  }
`;

import React from 'react';
import styled from '@benzinga/themetron';
import { WordpressWidget } from '@benzinga/content-manager';

// import { MoneyBlock } from './MoneyBlock';
import { MoneyBlocks } from './MoneyBlocks';
import { NoFirstRender } from '@benzinga/hooks';

import { LayoutAdmin } from '@benzinga/blocks';

export interface MoneyWidgetProps {
  widget: WordpressWidget;
  hideAdminBar?: boolean;
}

export const MoneyWidget: React.FC<MoneyWidgetProps> = ({ hideAdminBar = false, widget }) => {
  return (
    <MoneyWidgetWrapper>
      <NoFirstRender>
        <React.Suspense>{widget && !hideAdminBar && <LayoutAdmin post={widget} />}</React.Suspense>
      </NoFirstRender>
      <MoneyBlocks blocks={widget?.blocks} />
    </MoneyWidgetWrapper>
  );
};

export const MoneyWidgetWrapper = styled.div`
  width: 100%;
  overflow: hidden;
`;

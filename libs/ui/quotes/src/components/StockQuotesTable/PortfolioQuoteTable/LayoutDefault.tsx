'use client';
import React, { useEffect } from 'react';
import styled, { useTheme } from '@benzinga/themetron';
import Link from 'next/link';
import { DelayedQuote } from '@benzinga/quotes-manager';
import { getColorByValue } from '@benzinga/frontend-utils';
import { formatLarge } from '@benzinga/utils';
import { PortfolioQuoteTableEdgeCTA } from './PortfolioQuoteTableEdgeCTA';
import { WatchlistTableButton } from './WatchlistTableButton';
import { AddAllWatchlistTableButton } from './AddAllWatchlistTableButton';
import { usePermission } from '@benzinga/user-context';
import { SessionContext } from '@benzinga/session-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { useSearchParams } from 'next/navigation';

export interface LayoutDefaultProps {
  bypassPaywall?: boolean;
  paywall_label?: string;
  quotes?: DelayedQuote[];
  symbols?: string[];
}

export const LayoutDefault: React.FC<LayoutDefaultProps> = ({
  bypassPaywall = false,
  paywall_label = 'Perfect Stock Portfolio',
  quotes,
  symbols,
}) => {
  const theme = useTheme();
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const [isPaywallActive, setIsPaywallActive] = React.useState(false);
  const searchParams = useSearchParams();
  const slug = searchParams?.get('slug')?.replaceAll('-', '_');
  const session = React.useContext(SessionContext);

  useEffect(() => {
    if (!bypassPaywall) {
      if (!hasPermission) {
        setIsPaywallActive(true);
        session.getManager(TrackingManager).trackPaywallEvent('view', {
          paywall_id: 'layout-default-screener-table-benzinga-edge',
          paywall_type: 'hard',
          placement: slug ?? 'screener',
        });
      } else {
        setIsPaywallActive(false);
      }
    }
  }, [hasPermission, bypassPaywall, session, slug]);

  if (!quotes) {
    return <></>;
  }

  return (
    <LayoutDefaultContainer>
      <div className="stock-quotes-table-container font-manrope calendar-container relative bg-white">
        <table className="table-auto">
          <thead>
            <tr>
              <th colSpan={1}>
                <div className="cell-item">Ticker</div>
              </th>
              <th colSpan={1}>
                <div className="cell-item">Name</div>
              </th>
              <th colSpan={1}>
                <div className="cell-item">Last Price</div>
              </th>
              <th colSpan={1}>
                <div className="cell-item">Change</div>
              </th>
              <th colSpan={1}>
                <div className="cell-item">Market Cap</div>
              </th>
              <th colSpan={1}>
                <div className="cell-item">P/E Ratio</div>
              </th>
              <th colSpan={1}>
                <div className="cell-item">Avg Vol</div>
              </th>
              <th colSpan={1}>
                {symbols && symbols.length > 0 && (
                  <div className="cell-item">
                    <AddAllWatchlistTableButton isActive={isPaywallActive} symbols={symbols} />
                  </div>
                )}
              </th>
            </tr>
          </thead>
          <tbody>
            {quotes &&
              quotes.length > 0 &&
              quotes.map((quote, i) =>
                quote ? (
                  <tr className={isPaywallActive ? 'blur-sm' : ''} key={`quote-${i}`}>
                    <td className="ticker-data">
                      <div className="cell-item">
                        <div className="flex info-wrap gap-3">
                          <div className="company-info flex flex-col">
                            <div className="flex items-center gap-2">
                              <Link href={`/quote/${quote.symbol}`}>
                                <span className="symbol font-semibold">{quote.symbol}</span>{' '}
                              </Link>
                            </div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td>
                      <div className="cell-item">
                        <div className="flex info-wrap gap-3">
                          <div className="company-info flex flex-col">
                            <div className="industry">{quote.name}</div>
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="font-semibold">
                      <div className="cell-item">{quote?.lastTradePrice ? `$${quote.lastTradePrice}` : '-'}</div>
                    </td>
                    <td className="font-semibold">
                      <div className="cell-item">
                        <div className="flex gap-1">
                          <span
                            className="change"
                            style={{ color: getColorByValue(theme, quote.change) }}
                            title={quote.change?.toString()}
                          >
                            ${quote.change ?? '– '}
                          </span>
                          <span
                            className="change-percent"
                            style={{
                              backgroundColor: `${getColorByValue(theme, quote.changePercent)}20`,
                              color: getColorByValue(theme, quote.changePercent),
                            }}
                            title={quote.changePercent?.toString()}
                          >
                            {quote.changePercent ?? '– '}%
                          </span>
                        </div>
                      </div>
                    </td>
                    <td className="font-semibold">
                      <div className="cell-item">{quote?.marketCap ? `$${formatLarge(quote.marketCap)}` : '-'}</div>
                    </td>
                    <td className="font-semibold">
                      <div className="cell-item">{quote?.pe ? quote?.pe.toFixed(2) : '-'}</div>
                    </td>
                    <td className="font-semibold">
                      <div className="cell-item">
                        {quote?.averageVolume ? Number(quote.averageVolume.toFixed(2)).toLocaleString('en') : '-'}
                      </div>
                    </td>
                    <td>
                      <div className="cell-item">
                        <WatchlistTableButton isActive={isPaywallActive} symbol={quote.symbol} />
                      </div>
                    </td>
                  </tr>
                ) : null,
              )}
          </tbody>
        </table>
        {isPaywallActive && (
          <div className="absolute top-1/2 w-full -translate-y-2/4">
            <PortfolioQuoteTableEdgeCTA
              t_code={'be32be9dimibe3bz83'}
              title={paywall_label ? paywall_label : 'Perfect Stock Portfolio'}
              utm={'PaywallPerfectStock'}
            />
          </div>
        )}
      </div>
      <div className="bz-pro-action pt-2">
        <a href="https://www.benzinga.com/go/benzinga-pro" rel="noreferrer" target="_blank">
          Start Your Free 14-Day Benzinga Pro Trial
        </a>
      </div>
    </LayoutDefaultContainer>
  );
};

const LayoutDefaultContainer = styled.div`
  .stock-quotes-table-container {
    width: 100%;
    display: block;
    overflow-x: auto;
    clear: both;
    border: 1px solid #e1ebfa;
    border-radius: 4px;
    min-height: 600px;
    max-height: 800px;

    table {
      width: 100%;
      margin-bottom: 0.2rem;
      table-layout: auto;
      display: table;
      height: 100%;
      .cell-item {
        border-left: 1px solid #e1ebfa;
        padding-left: 12px;
      }
    }

    thead {
      border-bottom: 1px solid #e1ebfa;
      background: #f2f8ff;

      tr {
        text-align: left;
        th {
          color: #5b7292;
          padding: 12px 10px 12px 0px;
          min-width: 120px;
          &:first-of-type {
            .cell-item {
              border: none;
            }
          }

          &:nth-child(2) {
            min-width: 220px;
          }
          &:last-of-type {
            min-width: 60px;
          }
        }
      }
    }

    tbody {
      overflow: auto;

      tr {
        border-collapse: collapse;
        box-sizing: inherit;
        border-bottom: 1px solid #e1ebfa;
        width: 100%;

        td {
          padding: 6px 10px 6px 0;
          color: ${({ theme }) => theme.colorPalette.black};
          &:first-of-type {
            .cell-item {
              border: none;
            }
          }
          &:nth-child(2) {
            min-width: 220px;
          }
          &.ticker-data {
            img {
              width: 35px;
            }
            .company-info {
              a {
                color: #000000;
              }
              .company-name {
                padding: 2px 4px;
                border-radius: 4px;
                background: #e1ebfa;
                font-size: 12px;
                line-height: 16px;
              }
              .industry {
                font-size: 0.8rem;
                color: #395173;
              }
            }
          }
          &.price {
            .change-percent {
              padding: 0px 4px;
              border-radius: 4px;
            }
          }
          &.button-wrap {
            width: 120px;
          }
        }
        &:last-of-type {
          border: none;
        }
      }
    }
  }
`;

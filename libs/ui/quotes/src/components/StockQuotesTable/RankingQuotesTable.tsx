'use client';
import React, { useCallback, useEffect } from 'react';
import { useSearchParams } from 'next/navigation';
import { RxLockClosed } from 'react-icons/rx';
import Link from 'next/link';

import { SessionContext } from '@benzinga/session-context';
import { usePermission } from '@benzinga/user-context';
import { TrackingManager } from '@benzinga/tracking-manager';
import { QuotesManager } from '@benzinga/quotes-manager';
import { QuoteProtos, ScannerProtos } from '@benzinga/scanner-manager';
import { RankingColumn } from '@benzinga/blocks-utils';

import styled from '@benzinga/themetron';
import { Pagination } from '@benzinga/core-ui';
import { AddToWatchlist } from '@benzinga/watchlist-ui';
import { formatLarge } from '@benzinga/utils';
import { Tooltip } from '@benzinga/core-ui';
import { AuthContainer } from '@benzinga/auth-ui';

export interface RankingTable {
  rankings: QuoteProtos.IQuote[];
  columns: RankingColumn[];
  query: ScannerProtos.IQuery;
}

export interface RankingTableProps {
  bypassPaywall?: boolean;
  table?: RankingTable;
}

const SEARCH_ITEM_LOGO_SIZE = 40;
const IMAGE_REQUEST_PARAMS = {
  fields: 'mark_vector_light,logo_vector_light',
  scale: `${SEARCH_ITEM_LOGO_SIZE}x${SEARCH_ITEM_LOGO_SIZE}`,
  search_keys_type: 'symbol',
};

const lockedCTA = url => {
  return (
    <Link className="flex flex-row items-center justify-start text-gray-500 gap-2" href={url} target="_blank">
      <Tooltip content={'Click to Unlock'} position="top" trigger="hover">
        <RxLockClosed className="text-gray-500 mr-2" size={20} />
        <span className="text-gray-500">****</span>
      </Tooltip>
    </Link>
  );
};

export const RankingQuotesTable: React.FC<RankingTableProps> = ({ bypassPaywall = false, table }) => {
  const pageSize = 20;
  const [rows, setRows] = React.useState(table?.rankings.slice(0, pageSize));
  const [page, setPage] = React.useState(1);
  const [showAllData, setShowAllData] = React.useState<boolean>(false);
  const [tickerLogos, setTickerLogos] = React.useState({});

  // Skip symbol field to show combined data for first column
  const columns = table?.columns && table.columns.length > 1 ? table.columns.slice(1) : [];

  const session = React.useContext(SessionContext);
  const quotesManager = session.getManager(QuotesManager);
  const hasPermission = usePermission('com/read', 'unlimited-calendars');
  const searchParams = useSearchParams();
  const slug = searchParams?.get('slug')?.replaceAll('-', '_');
  const url = `https://www.benzinga.com/premium/ideas/benzinga-edge-7-days-free-trial/?adType=${slug}&ad=ticker&campaign=ranking`;

  const getRankingLogos = useCallback(
    async symbols => {
      if (symbols.length > 0) {
        const { ok: logos } = await quotesManager.getQuotesLogosMapped(symbols, IMAGE_REQUEST_PARAMS);
        if (logos) {
          setTickerLogos(logos);
        }
      }
    },
    [quotesManager],
  );

  useEffect(() => {
    if (hasPermission || bypassPaywall) {
      setShowAllData(true);
    } else {
      session.getManager(TrackingManager).trackPaywallEvent('view', {
        paywall_id: 'edge-ranking-table-benzinga-edge',
        paywall_type: 'hard',
        placement: slug ?? 'screener',
      });
    }
    getRankingLogos(table?.rankings.map(item => item?.symbol));
  }, [hasPermission, session, slug, bypassPaywall, table?.rankings, getRankingLogos]);

  const handlePageChange = ({ nextPage, pageSize }) => {
    setPage(nextPage);
    const startIdx = (nextPage - 1) * pageSize;
    const endIdx = startIdx + pageSize;
    const rankings = table?.rankings.slice(startIdx, endIdx);
    setRows(rankings);
  };

  return (
    <>
      <Container className="stock-ranking-table-container font-manrope">
        {rows?.length === 0 && (
          <div className="text-center mx-auto p-8 bg-bzgrey-100 w-full rounded-md">
            No data found. Please try again later.
          </div>
        )}
        <table className="table-auto">
          <thead>
            {rows && rows.length > 0 && (
              <tr className="">
                {table?.columns.map((column, i) => (
                  <th colSpan={1} key={`ranking-header-${i}`}>
                    {column.label}
                  </th>
                ))}
              </tr>
            )}
          </thead>
          <tbody>
            {rows &&
              rows.map((row, i) => (
                <tr key={`ranking-row-${i}`}>
                  <td className="ticker-data">
                    <div className="flex info-wrap gap-3 overflow-hidden">
                      {(table?.columns?.[0]?.gated ? showAllData : true) &&
                      tickerLogos[row?.symbol ?? ''] &&
                      tickerLogos[row?.symbol ?? ''].files?.logo_vector_light ? (
                        <img alt={`${row.symbol} logo`} src={tickerLogos[row?.symbol ?? ''].files?.logo_vector_light} />
                      ) : (
                        <div></div>
                      )}
                      <div className="company-info flex flex-col">
                        <div className="flex flex-row items-center gap-2">
                          {(table?.columns?.[0]?.gated ? showAllData : true) ? (
                            <Link
                              className="flex flex-row items-center gap-2"
                              href={(table?.columns?.[0]?.gated ? showAllData : true) ? `/quote/${row.symbol}` : url}
                              target="_blank"
                            >
                              <span className="symbol font-semibold">{row.symbol}</span>
                              <div className="company-name">{row.name}</div>
                            </Link>
                          ) : (
                            lockedCTA(url)
                          )}
                        </div>
                        <div className="industry">{row.gicsSectorName}</div>
                      </div>
                    </div>
                  </td>
                  {columns.map((column, j) => {
                    const hideValue = column.gated && !showAllData;
                    const value = row[column.field];
                    let cellValue = value;

                    if (hideValue) {
                      return <td>{lockedCTA(url)}</td>;
                    }

                    if (typeof value === 'number') {
                      cellValue = cellValue.toFixed(2);
                    }
                    if (column.format === 'longPrice') {
                      cellValue = `$${formatLarge(Number(cellValue))}`;
                    }
                    if (column.format === 'price') {
                      cellValue = `$${cellValue}`;
                    }
                    if (column.format === 'percent') {
                      cellValue = `${cellValue}%`;
                    }

                    return (
                      <td className={`${column.bold ? 'font-bold' : ''}`} key={`ranking-${i}-${j}`}>
                        {value ? cellValue : '–'}
                      </td>
                    );
                  })}
                  {showAllData && (
                    <td className="button-wrap">
                      <AddToWatchlist
                        buttonText={'Add to Watchlist'}
                        buttonVariant="flat-light-blue"
                        showIcon={true}
                        symbol={row?.symbol ?? ''}
                        variant="default"
                      />
                    </td>
                  )}
                </tr>
              ))}
          </tbody>
        </table>
      </Container>
      <Pagination
        buttonClassName="pagination-button my-2 rounded-[4px]"
        defaultPage={page}
        onPageChanged={handlePageChange}
        pageSize={pageSize}
        totalItems={table?.rankings?.length}
      />
      <div className="bz-pro-action">
        <a href="https://www.benzinga.com/go/benzinga-pro" rel="noreferrer" target="_blank">
          Start Your Free 14-Day Benzinga Pro Trial
        </a>
      </div>
      {!hasPermission && (
        <AuthContainer
          authMode="login"
          contentType="rankings"
          iterationStyle="edge-hard"
          placement="ranking"
          preventRedirect={true}
        />
      )}
    </>
  );
};

const Container = styled.div`
  &.stock-ranking-table-container {
    width: 100%;
    display: block;
    overflow-x: auto;
    clear: both;
    table {
      width: 100%;
      margin-bottom: 0.2rem;
      table-layout: auto;
      display: table;
      height: 100%;
    }

    thead {
      border-bottom: 1px solid #e1ebfa;
      font-weight: bold;

      tr {
        text-align: left;
        th {
          padding: 4px 8px;
        }
      }
    }

    tbody {
      overflow: auto;

      tr {
        border-collapse: collapse;
        box-sizing: inherit;
        border-bottom: 1px solid #e1ebfa;
        width: 100%;

        td {
          padding: 6px;
          color: ${({ theme }) => theme.colorPalette.black};
          &.ticker-data {
            img {
              width: 35px;
            }
            .company-info {
              a {
                color: #000000;
              }
              .company-name {
                padding: 2px 4px;
                border-radius: 4px;
                background: #e1ebfa;
                font-size: 12px;
                line-height: 16px;
                max-width: 200px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .industry {
                font-size: 0.8rem;
                color: #395173;
              }
            }
          }
          &.price {
            .change-percent {
              padding: 0px 4px;
              border-radius: 4px;
            }
          }
          &.button-wrap {
            width: 120px;
          }
        }
      }

      tr:hover {
        background-color: rgb(***********);
      }
    }
  }
`;

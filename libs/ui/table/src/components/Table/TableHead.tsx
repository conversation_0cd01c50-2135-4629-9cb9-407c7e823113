import React from 'react';
import styled from '@benzinga/themetron';
import type { ColumnDef } from '../../interface';
import type { SortBy } from '../../libs/utils';
import { Tooltip } from '@benzinga/core-ui';

export interface TableHeadProps {
  columnsDef: ColumnDef[];
  onSortChange?: (newSortByField: string, columnDef: ColumnDef) => void;
  hiddenColumnsWhenGated?: Set<string>;
  sortBy?: SortBy;
  style?: React.CSSProperties;
  isGated?: boolean;
  $alignLastRowsRight?: boolean;
  $hasStockTableStyling?: boolean;
}

export const TableHead: React.FC<TableHeadProps> = ({
  columnsDef,
  hiddenColumnsWhenGated,
  isGated,
  onSortChange,
  sortBy,
  style,
  ...props
}) => {
  return (
    <THeadStyled {...props} className="benzinga-core-table-thead whitespace-nowrap" style={style}>
      <tr>
        {columnsDef &&
          columnsDef.map((column, index) => {
            if (isGated && hiddenColumnsWhenGated?.has(column.field)) return null;
            return (
              <th
                aria-hidden={column.ariaHidden}
                className="benzinga-core-table-th capitalize"
                colSpan={column.colSpan ? column.colSpan : 1}
                key={`table-head-${column.field}-${index}`}
              >
                <Tooltip content={column.headerTooltip} width={160}>
                  {sortBy && column.field && column.sortEnabled && onSortChange ? (
                    <button
                      className="flex items-center justify-between w-full leading-none"
                      onClick={() => onSortChange(column.field, column)}
                    >
                      <span className="capitalize">{column.headerName ?? (column.field ? column.field : '')}</span>
                      {sortBy && <HeaderSortIcons field={column.field} sortBy={sortBy} />}
                    </button>
                  ) : (
                    column.headerName ?? (column.field ? column.field : '')
                  )}
                </Tooltip>
              </th>
            );
          })}
      </tr>
    </THeadStyled>
  );
};

export const HeaderSortIcons: React.FC<{ field: string; sortBy: SortBy }> = ({ field, sortBy }) => {
  return (
    <div
      aria-label={`Sort by field: ${field}, in ${sortBy.direction} order`}
      className="header-sort-icons flex flex-col"
    >
      <span
        className={`header-sort-icon header-sort-icon__up h-1.5 text-[6px] ml-1 ${
          field === sortBy.field && sortBy.direction === 'desc' ? 'text-blue-500' : ''
        }`}
        role="presentation"
      >
        ▲
      </span>
      <span
        className={`header-sort-icon header-sort-icon__down h-1.5 text-[6px] ml-1 ${
          field === sortBy.field && sortBy.direction === 'asc' ? 'text-blue-500' : ''
        }`}
        role="presentation"
      >
        ▼
      </span>
    </div>
  );
};

export const THeadStyled = styled.thead<{ $alignLastRowsRight?: boolean; $hasStockTableStyling?: boolean }>`
  width: 100%;
  font-size: 0.725rem;
  font-weight: 600;
  min-height: 35px;
  height: 35px;

  tr {
    text-align: left;
    border-collapse: collapse;
    color: ${({ theme }) => theme.colorPalette.white};
    background-color: ${({ $hasStockTableStyling, theme }) =>
      $hasStockTableStyling ? '#072232' : theme.colorPalette.blue500};
    border-bottom: ${({ $hasStockTableStyling, theme }) =>
      $hasStockTableStyling ? '3px solid #2ca2d1' : `2px solid ${theme.colors.border}`};
    position: sticky;
    top: 0;
    z-index: 1;

    th {
      border-left: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : 'none')};
      &:first-child {
        border-left: none;
        border-left: ${({ $hasStockTableStyling, theme }) =>
          $hasStockTableStyling ? '1px solid #072232' : `1px solid ${theme.colorPalette.blue500}`};
      }
      &:last-of-type {
        text-align: ${({ $alignLastRowsRight }) => ($alignLastRowsRight ? 'right' : 'unset')};
        border-right: ${({ $hasStockTableStyling, theme }) =>
          $hasStockTableStyling ? '1px solid #072232' : `1px solid ${theme.colorPalette.blue500}`};
      }
    }
  }

  th,
  td {
    position: sticky;
    top: 0;
    z-index: 1;
  }
`;

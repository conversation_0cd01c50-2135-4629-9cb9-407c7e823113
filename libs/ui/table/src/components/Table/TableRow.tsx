'use client';
import React from 'react';
import styled from '@benzinga/themetron';
import classNames from 'classnames';
import { ColumnDef } from '../../interface';
import { getCellRendererRowValue, getRowValue } from '../../libs/utils';
import { Tooltip } from '@benzinga/core-ui';

interface StockQuotesRowProps {
  columnsDef: ColumnDef[];
  colorRowByValue?: boolean;
  colorRowField?: string;
  data: any;
  hiddenColumnsWhenGated?: Set<string>;
  index: number;
  isGated?: boolean;
  isPayWall?: boolean;
  $hasStockTableStyling?: boolean;
}

export const TableRow: React.FC<StockQuotesRowProps> = ({
  colorRowByValue = false,
  colorRowField = '',
  columnsDef,
  data,
  hiddenColumnsWhenGated,
  index,
  isGated,
  isPayWall,
  ...props
}) => {
  if (!data || !Array.isArray(columnsDef)) return null;

  const rowClassName =
    colorRowByValue && data[colorRowField]
      ? data[colorRowField] > 0
        ? 'row-green'
        : data[colorRowField] < 0
          ? 'row-red'
          : ''
      : '';

  return (
    <StyledTableRow className={`benzinga-core-table-row ${rowClassName} ${isGated ? 'blur-md' : ''}`} {...props}>
      {columnsDef?.map((column, i) => {
        if (isPayWall && hiddenColumnsWhenGated?.has(column.field)) return null;
        const rowValue = getRowValue(column, index, data);
        const cellRenderer = getCellRendererRowValue(column, index, data);
        const rawValue: string | number =
          (typeof column.valueGetter === 'function' && column.valueGetter({ data })) ||
          data[column.field] ||
          column.shouldRender ||
          null;

        const toolTip: string | null =
          (typeof column.tooltipValueGetter === 'function' && column.tooltipValueGetter({ data })) || null;

        const cellClassname =
          typeof column.cellClass === 'function'
            ? column.cellClass({ data, index, value: rawValue })
            : column.cellClass
              ? column.cellClass
              : '';

        const className = classNames(`table-cell-${column.field} max-w-[300px]`, {
          [`${cellClassname}`]: !!cellClassname,
        });

        const title =
          data[column.field] || (['number', 'string'].includes(typeof rowValue) ? rowValue?.toString() : '');

        return (
          <StyledTableDataCell
            $width={column.width}
            aria-hidden={column?.ariaHidden}
            className={
              isGated
                ? `blur-sm pointer-events-none select-none ${className}`
                : className + ' relative group overflow-visible'
            }
            colSpan={column?.colSpan ?? 1}
            key={i}
            title={column.shouldRender ? undefined : title.toString()}
          >
            <Tooltip content={toolTip} isContentHtml width={column?.tooltipWidth ?? 100}>
              {cellRenderer || '—'}
            </Tooltip>
          </StyledTableDataCell>
        );
      })}
    </StyledTableRow>
  );
};

export const StyledTableRow = styled.tr<{ $hasStockTableStyling?: boolean }>`
  border-collapse: collapse;
  box-sizing: inherit;
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  overflow: visible;
  color: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '#333333' : '#000000')};
  border-bottom: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : '1px solid #f0f0f0')};

  &:last-of-type {
    border-bottom: unset;
  }

  td {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    border-right: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '1px solid #aaa' : 'unset')};

    &:first-of-type {
      border-left: unset;
    }

    &:last-of-type {
      border-right: unset;
      text-align: right;
    }
  }

  &:nth-child(odd) {
    background: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '#efefef' : `#f9f9f9`)};
  }

  &:hover {
    background: ${({ $hasStockTableStyling }) => ($hasStockTableStyling ? '#40c0f319' : '#efefef')};

    .gain-cell {
      background: #cef1ce;
    }

    .lose-cell {
      background: #fdcece;
    }
  }

  .gain-cell {
    color: ${({ $hasStockTableStyling, theme }) => ($hasStockTableStyling ? '#009900' : theme.colorPalette.green500)};
    background: #f2faf2;
  }

  .lose-cell {
    color: ${({ $hasStockTableStyling, theme }) => ($hasStockTableStyling ? '#cc0000' : theme.colorPalette.red500)};
    background: #f9e5e5;
  }
`;

const StyledTableDataCell = styled.td<{ $width?: number }>`
  max-width: ${({ $width }) => ($width ? `${$width}px` : undefined)};
`;

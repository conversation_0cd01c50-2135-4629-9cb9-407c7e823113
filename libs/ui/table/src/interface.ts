type RowValue = any;

export interface ColumnDef<T = any> {
  cellRenderer?: ({ data, index, value }: { data: T; index: number; value: RowValue }) => JSX.Element | string | null;
  cellClass?: (({ data, index, value }: { data: T; index: number; value?: RowValue }) => string) | string;
  field: string;
  headerName: string;
  valueFormatter?: ({ data, index, value }: { data: any; index: number; value: RowValue }) => string;
  valueGetter?: ({ data }: { data: T }) => string | number;
  colSpan?: number;
  ariaHidden?: boolean;
  sortEnabled?: boolean;
  shouldRender?: boolean;
  width?: number;
  cellRendererParams?: {
    tickerDescriptionKey?: string;
    tickerSymbolKey?: string;
  };
  tooltipValueGetter?: ({ data }: { data: T }) => string;
  tooltipWidth?: number;
  headerTooltip?: string;
}

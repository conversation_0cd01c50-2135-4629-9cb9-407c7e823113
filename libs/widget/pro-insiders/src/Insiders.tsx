'use client';
import React from 'react';
import { InsidersGrid } from './InsidersGrid';

import { LoadingOutlined } from '@ant-design/icons';

import { PermissionOverlay, SendLinkContextProvider } from '@benzinga/pro-ui';
import { TableParameters } from '@benzinga/ag-grid-utils';
import { SessionContext } from '@benzinga/session-context';
import {
  InsiderTradePreset,
  InsiderTradesManager,
  InsidersFiling,
  InsidersFilingResult,
  InsidersTradeFiling,
} from '@benzinga/insider-trades-manager';
import { SafeError } from '@benzinga/safe-await';
import { Toolbar } from './toolbar';
import { useWidgetParameters } from '@benzinga/widget-tools';
import { InsidersWidgetManifest } from './widget';
import { WidgetLinkingID } from '@benzinga/widget-linking';
import { InsidersConfig } from './entity';
/**
 * Main insiders widget. Minus a settings button, which is handled from InsidersWidget.
 */

const mapEmptyString = (str: string | undefined) => (!str || str === 'NONE' || str === 'N/A' ? undefined : str);
export const Insiders: React.FC = () => {
  const session = React.useContext(SessionContext);
  const widgetParams = useWidgetParameters(InsidersWidgetManifest);
  const [state, setState] = React.useState({
    err: undefined as SafeError | undefined,
    lastUpdated: undefined as undefined | Date,
    records: [] as InsidersTradeFiling[],
  });

  const pullData = React.useCallback(async () => {
    const results = await (() => {
      switch (widgetParams.parameters.preset.type) {
        case 'config':
          return session.getManager(InsiderTradesManager).getProPresetInsiderTrade({
            limit: 100,
            preset1: widgetParams.parameters.preset.id,
          });
        case 'stock':
          return session.getManager(InsiderTradesManager).getInsiderTrades({
            company_ticker: widgetParams.parameters.preset.id,
          });
      }
    })();

    if (results.err) {
      setState({
        err: results.err,
        lastUpdated: new Date(),
        records: [],
      });
    } else {
      const records = (
        widgetParams.parameters.preset.type === 'stock'
          ? (results.ok as InsidersFilingResult).filings.filings
          : widgetParams.parameters.preset.type === 'config'
            ? (results.ok.filings[0] as InsiderTradePreset).filings
            : []
      ).map((filing: InsidersFiling) => ({
        ...filing,
        company_ticker: mapEmptyString(filing.company_ticker),
      }));

      setState({
        err: undefined,
        lastUpdated: new Date(),
        records,
      });
    }
  }, [widgetParams.parameters.preset.id, widgetParams.parameters.preset.type, session]);

  React.useEffect(() => {
    pullData();
  }, [pullData]);

  const setParams = React.useCallback(
    (newPrems: Partial<typeof InsidersWidgetManifest.defaultWidgetParameters>) => {
      const setParameters = widgetParams.setParameters;
      setParameters(old => ({
        ...old,
        ...newPrems,
      }));
    },
    [widgetParams.setParameters],
  );

  const setSendGroup = React.useCallback(
    (groupId: WidgetLinkingID) => {
      setParams({
        sendGroup: groupId,
      });
    },
    [setParams],
  );

  const setFlightMode = React.useCallback(
    (flightMode: boolean) => {
      setParams({
        flightMode,
      });
    },
    [setParams],
  );

  const configChanged = React.useCallback(
    (config: InsidersConfig) => {
      setParams({
        preset: config,
      });
    },
    [setParams],
  );

  const updateTableParams = React.useCallback(
    (table: Partial<TableParameters>) => {
      setParams({
        table: {
          ...widgetParams.parameters.table,
          ...table,
        },
      });
    },
    [widgetParams.parameters.table, setParams],
  );

  const rowDoubleClicked = React.useCallback((filing: InsidersFiling) => {
    if (!filing.url) {
      return;
    }
    window.open(filing.url);
  }, []);

  if (!state.lastUpdated) {
    return (
      <div className="insiders">
        <LoadingOutlined spin style={{ fontSize: 14 }} /> Loading...
      </div>
    );
  }

  return (
    <div className="Widget-content insiders">
      <SendLinkContextProvider displayModal={false} linkId={widgetParams.parameters.sendGroup} setLink={setSendGroup}>
        {
          <Toolbar
            config={widgetParams.parameters.preset}
            flightMode={widgetParams.parameters.flightMode ?? false}
            onConfigChanged={configChanged}
            pullData={pullData}
            setFlightMode={setFlightMode}
            setGroup={setSendGroup}
            state={state}
          />
        }
        <PermissionOverlay permission={{ action: 'bzpro/widget/use', resource: 'insider-trades' }}>
          <div className="Widget-body">
            <div className="ag-theme-benzinga" style={{ flex: '1', position: 'relative' }}>
              <InsidersGrid
                gridLayout={widgetParams.parameters.table}
                onGridLayoutChanged={updateTableParams}
                onRowDoubleClicked={rowDoubleClicked}
                rowData={state.records}
              />
            </div>
          </div>
        </PermissionOverlay>
      </SendLinkContextProvider>
    </div>
  );
};

export default Insiders;
